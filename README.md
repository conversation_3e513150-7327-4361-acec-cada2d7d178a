# <PERSON><PERSON><PERSON><PERSON><PERSON> - Portfolio

Welcome to my professional portfolio! This is a modern, interactive, and AI-powered portfolio showcasing my skills, projects, and experience in the tech space.

## Features

- **Interactive 3D Elements**: Engaging three-dimensional visualizations using Three.js
- **AI-Powered Chatbot**: Virtual assistant to help visitors navigate the portfolio
- **Responsive Design**: Seamless experience across all devices
- **Modern UI/UX**: Clean, intuitive interface with smooth animations
- **Project Showcase**: Detailed presentations of my work with interactive elements

## Tech Stack

- **Frontend Framework**: Next.js (React)
- **3D Visualization**: Three.js with React Three Fiber
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/BhekumusaEric/My-Portfolio.git
   cd My-Portfolio
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the portfolio.

## Project Structure

- `pages/`: Next.js pages
- `components/`: Reusable React components
- `public/`: Static assets
- `styles/`: Global styles and CSS modules
- `lib/`: Utility functions and custom hooks

## Contact

Feel free to reach out to me:

- **Email**: <EMAIL>
- **LinkedIn**: [Bhekumusa Eric Ntshwenya](https://www.linkedin.com/in/bhekumusaerickelvinntshwenya/)
- **GitHub**: [BhekumusaEric](https://github.com/BhekumusaEric)
- **Twitter**: [@NoahEric_](https://x.com/NoahEric_)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
