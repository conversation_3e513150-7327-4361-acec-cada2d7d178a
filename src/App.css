.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

.canvas-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
}

.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #0859b8;
  transform: translateY(-2px);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 768px) {
  .grid-cols-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) {
  .grid-cols-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 3D Coin Rotation Effect */
.coin-rotate {
  animation: rotate3d 20s linear infinite;
  transform-style: preserve-3d;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(9, 105, 218, 0.5);
  transition: all 0.3s ease;
}

.coin-rotate:hover {
  box-shadow: 0 0 30px rgba(9, 105, 218, 0.8);
  transform: scale(1.05);
}

.coin-container {
  perspective: 1000px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.coin-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(9, 105, 218, 0.2), transparent);
  pointer-events: none;
}

@keyframes rotate3d {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}
