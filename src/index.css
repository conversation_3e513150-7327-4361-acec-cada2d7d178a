:root {
  --primary-color: #0969DA;
  --secondary-color: #1F222E;
  --accent-color: #F8D866;
  --dark-color: #121212;
  --light-color: #f5f5f5;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--dark-color);
  color: var(--light-color);
  line-height: 1.6;
  overflow-x: hidden;
}

code, pre {
  font-family: 'Fira Code', monospace;
}

a {
  color: inherit;
  text-decoration: none;
}

ul {
  list-style: none;
}

button, input, textarea {
  font-family: inherit;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 6rem 0;
}

.gradient-text {
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }
}
