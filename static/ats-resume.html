<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> - Resume</title>
    <style>
        /* ATS-friendly styling */
        body {
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .resume-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 24px;
            margin: 0 0 5px 0;
            color: #2a63b8;
        }
        
        h2 {
            font-size: 18px;
            margin: 0 0 5px 0;
            color: #2a63b8;
            border-bottom: 1px solid #2a63b8;
            padding-bottom: 5px;
        }
        
        h3 {
            font-size: 16px;
            margin: 0 0 5px 0;
        }
        
        p {
            margin: 0 0 10px 0;
        }
        
        .contact-info {
            margin-bottom: 5px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .job, .education, .project {
            margin-bottom: 15px;
        }
        
        .job-title, .degree, .project-title {
            font-weight: bold;
        }
        
        .company, .school, .project-tech {
            font-style: italic;
        }
        
        .date {
            color: #666;
        }
        
        ul {
            margin: 5px 0 10px 20px;
            padding: 0;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            margin: 0;
            padding: 0;
            list-style-type: none;
        }
        
        .skills-list li {
            margin-right: 20px;
        }
        
        .print-button {
            background-color: #2a63b8;
            color: white;
            border: none;
            padding: 10px 15px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            display: block;
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .resume-container {
                box-shadow: none;
                padding: 0;
            }
            
            .print-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <div class="header">
            <h1>BHEKUMUSA ERIC NTSHWENYA</h1>
            <div class="contact-info">
                Phone: +27 67 137 2148 | Email: <EMAIL>
            </div>
            <div class="contact-info">
                LinkedIn: linkedin.com/in/bhekumusaerickelvinntshwenya | GitHub: github.com/BhekumusaEric
            </div>
            <div class="contact-info">
                Location: Johannesburg, South Africa
            </div>
        </div>
        
        <div class="section">
            <h2>PROFESSIONAL SUMMARY</h2>
            <p>Software Developer with experience in AI/ML technologies, web development, and mobile applications. Skilled in Python, JavaScript, and various frameworks. Passionate about creating innovative solutions to real-world problems through technology. Strong problem-solving abilities and commitment to continuous learning.</p>
        </div>
        
        <div class="section">
            <h2>TECHNICAL SKILLS</h2>
            <ul class="skills-list">
                <li>Python</li>
                <li>JavaScript</li>
                <li>HTML/CSS</li>
                <li>React</li>
                <li>Django</li>
                <li>Flask</li>
                <li>SQL</li>
                <li>Machine Learning</li>
                <li>TensorFlow</li>
                <li>Data Analysis</li>
                <li>Git</li>
                <li>RESTful APIs</li>
                <li>Azure</li>
                <li>Blockchain</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>WORK EXPERIENCE</h2>
            
            <div class="job">
                <div class="job-title">Teacher / Tutor</div>
                <div class="company">Central Johannesburg TVET College</div>
                <div class="date">September 2023 - Present</div>
                <ul>
                    <li>Developed and delivered curriculum for financial services training, including ATM assistance, money management, and bank teller operations</li>
                    <li>Created comprehensive learning materials to enhance student understanding of financial concepts</li>
                    <li>Provided personalized tutoring to students, improving their comprehension and performance</li>
                    <li>Implemented assessment strategies to track student progress and identify areas for improvement</li>
                </ul>
            </div>
            
            <div class="job">
                <div class="job-title">Data Capturer</div>
                <div class="company">Believers Care Society Academy</div>
                <div class="date">2020 (3-6 Months)</div>
                <ul>
                    <li>Processed and managed large volumes of data with high accuracy and attention to detail</li>
                    <li>Developed efficient data organization systems to improve workflow processes</li>
                    <li>Collaborated with team members to ensure data integrity and consistency</li>
                    <li>Assisted with administrative tasks and record-keeping, contributing to operational efficiency</li>
                </ul>
            </div>
            
            <div class="job">
                <div class="job-title">Technician</div>
                <div class="company">Book Dash</div>
                <div class="date">2019 (0-3 Months)</div>
                <ul>
                    <li>Provided technical support for events and operations, ensuring smooth execution</li>
                    <li>Set up and troubleshot equipment, resolving technical issues promptly</li>
                    <li>Collaborated with team members to coordinate technical aspects of events</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>PROJECTS</h2>
            
            <div class="project">
                <div class="project-title">SafeWayAI</div>
                <div class="project-tech">Python, Flet, Google Maps API, Firebase, Machine Learning</div>
                <ul>
                    <li>Developed an AI-powered emergency detection platform that provides safe routes to destinations and detects danger in real-time</li>
                    <li>Integrated Google Maps API for route visualization and Firebase for real-time data storage and retrieval</li>
                    <li>Implemented machine learning algorithms to analyze route safety based on crime data patterns</li>
                    <li>Created user-friendly interfaces for reporting incidents and viewing safety information</li>
                </ul>
            </div>
            
            <div class="project">
                <div class="project-title">Smart Loan Approval Predictor</div>
                <div class="project-tech">Python, Kivy, Machine Learning, SQLite</div>
                <ul>
                    <li>Built a mobile application that uses machine learning to predict loan approval probability</li>
                    <li>Designed intuitive user interfaces for data input and result visualization</li>
                    <li>Developed predictive models to analyze user financial data and determine loan eligibility</li>
                    <li>Implemented secure data storage using SQLite for user information management</li>
                </ul>
            </div>
            
            <div class="project">
                <div class="project-title">Eduwize</div>
                <div class="project-tech">Django, Azure Cognitive Services, Python, PostgreSQL</div>
                <ul>
                    <li>Created an AI-powered learning platform that provides personalized study material recommendations</li>
                    <li>Integrated Azure Cognitive Services for content analysis and intelligent recommendations</li>
                    <li>Developed performance tracking features to monitor student progress and identify areas for improvement</li>
                    <li>Implemented resource management system for organizing educational materials</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>EDUCATION</h2>
            
            <div class="education">
                <div class="degree">Software Engineering</div>
                <div class="school">WeThinkCode_</div>
                <div class="date">2023 - Present</div>
                <p>Intensive software engineering program focusing on practical coding skills, problem-solving, and industry-relevant technologies.</p>
            </div>
            
            <div class="education">
                <div class="degree">Information Technology and Computer Science (NQF Level 4)</div>
                <div class="school">Central Johannesburg TVET College</div>
                <div class="date">Expected 2025</div>
            </div>
            
            <div class="education">
                <div class="degree">Office Administration (NQF Level 3)</div>
                <div class="school">Believers Care Society Academy</div>
                <div class="date">2020</div>
            </div>
            
            <div class="education">
                <div class="degree">Grade 11</div>
                <div class="school">City Rand College</div>
                <div class="date">2017</div>
            </div>
        </div>
        
        <div class="section">
            <h2>CERTIFICATIONS & COURSES</h2>
            <ul>
                <li><strong>ICT Courses (August 2023):</strong> Help Desk Assistant, Data Capturer, Beginner Coder, Call Centre Operator, Computer Basics</li>
                <li><strong>Hospitality & Tourism Courses (September 2023):</strong> Waitron Basics, Receptionist Basics, Housekeeper, Baggage Porter Basics, Coffee Barista Basics, Communication Skills</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>LANGUAGES</h2>
            <ul class="skills-list">
                <li>English (Fluent)</li>
                <li>Swazi (Native)</li>
                <li>Zulu (Native)</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>REFERENCES</h2>
            <p><strong>Mr. Alex Iheme</strong> (Employer) - 073 538 2855</p>
            <p><strong>Ms. Julia Norr</strong> (Employer) - 074 464 5588</p>
            <p><strong>Ms. Massana Mabitsela</strong> (Teacher/Educator) - 073 132 8146</p>
        </div>
    </div>
    
    <button class="print-button" onclick="window.print()">Print Resume</button>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const printButton = document.querySelector('.print-button');
            printButton.addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html>
