{"version": 3, "sources": ["webpack://src/index.css", "webpack://src/App.css"], "names": [], "mappings": "AAAA,MACE,uBAAwB,CACxB,yBAA0B,CAC1B,sBAAuB,CACvB,oBAAqB,CACrB,qBACF,CAEA,EACE,qBAAsB,CACtB,QAAS,CACT,SACF,CAEA,KACE,8BAAgC,CAChC,kCAAmC,CACnC,wBAAyB,CACzB,eAAgB,CAChB,iBACF,CAEA,SACE,iCACF,CAEA,EACE,aAAc,CACd,oBACF,CAEA,GACE,eACF,CAEA,sBACE,mBACF,CAEA,WACE,UAAW,CACX,gBAAiB,CACjB,aAAc,CACd,cACF,CAEA,SACE,cACF,CAEA,eACE,0EAA6E,CAC7E,4BAA6B,CAC7B,mCAAoC,CACpC,oBACF,CAEA,cACE,8BAAqC,CACrC,0BAA2B,CAC3B,kCAAmC,CACnC,mCAA0C,CAC1C,kBACF,CAEA,yBACE,SACE,cACF,CACF,CCrEA,KACE,gBAAiB,CACjB,YAAa,CACb,qBACF,CAEA,cACE,QACF,CAEA,kBACE,cAAe,CACf,KAAM,CACN,MAAO,CACP,UAAW,CACX,YAAa,CACb,UACF,CAEA,KACE,oBAAqB,CACrB,oBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,uBACF,CAEA,aACE,qCAAsC,CACtC,UAAY,CACZ,WACF,CAEA,mBACE,wBAAyB,CACzB,0BACF,CAEA,aACE,wBAA6B,CAC7B,0BAA2B,CAC3B,qCACF,CAEA,mBACE,qCAAsC,CACtC,UAAY,CACZ,0BACF,CAEA,MACE,YACF,CAEA,UACE,qBACF,CAEA,cACE,kBACF,CAEA,gBACE,sBACF,CAEA,iBACE,6BACF,CAEA,aACE,iBACF,CAEA,MACE,YACF,CAEA,aACE,mCACF,CAEA,yBACE,gBACE,mCACF,CACF,CAEA,yBACE,gBACE,mCACF,CACF,CAGA,aACE,qCAAsC,CACtC,2BAA4B,CAC5B,iBAAkB,CAClB,sCAA2C,CAC3C,uBACF,CAEA,mBACE,sCAA2C,CAC3C,qBACF,CAEA,gBACE,kBAAmB,CACnB,iBAAkB,CAClB,eAAgB,CAChB,iBACF,CAEA,sBACE,UAAW,CACX,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,OAAQ,CACR,QAAS,CACT,gEAAuE,CACvE,mBACF,CAEA,oBACE,GACE,uBACF,CACA,GACE,wBACF,CACF", "file": "main.c8169329.chunk.css", "sourcesContent": [":root {\n  --primary-color: #0969DA;\n  --secondary-color: #1F222E;\n  --accent-color: #F8D866;\n  --dark-color: #121212;\n  --light-color: #f5f5f5;\n}\n\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  font-family: 'Inter', sans-serif;\n  background-color: var(--dark-color);\n  color: var(--light-color);\n  line-height: 1.6;\n  overflow-x: hidden;\n}\n\ncode, pre {\n  font-family: 'Fira Code', monospace;\n}\n\na {\n  color: inherit;\n  text-decoration: none;\n}\n\nul {\n  list-style: none;\n}\n\nbutton, input, textarea {\n  font-family: inherit;\n}\n\n.container {\n  width: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section {\n  padding: 6rem 0;\n}\n\n.gradient-text {\n  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.glass-effect {\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n}\n\n@media (max-width: 768px) {\n  .section {\n    padding: 4rem 0;\n  }\n}\n", ".app {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.main-content {\n  flex: 1;\n}\n\n.canvas-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  z-index: -1;\n}\n\n.btn {\n  display: inline-block;\n  padding: 0.8rem 1.5rem;\n  border-radius: 5px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-primary {\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n}\n\n.btn-primary:hover {\n  background-color: #0859b8;\n  transform: translateY(-2px);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: var(--primary-color);\n  border: 1px solid var(--primary-color);\n}\n\n.btn-outline:hover {\n  background-color: var(--primary-color);\n  color: white;\n  transform: translateY(-2px);\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.justify-between {\n  justify-content: space-between;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.grid {\n  display: grid;\n}\n\n.grid-cols-1 {\n  grid-template-columns: repeat(1, 1fr);\n}\n\n@media (min-width: 768px) {\n  .grid-cols-md-2 {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (min-width: 992px) {\n  .grid-cols-lg-3 {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n/* 3D Coin Rotation Effect */\n.coin-rotate {\n  animation: rotate3d 4s linear infinite;\n  transform-style: preserve-3d;\n  border-radius: 50%;\n  box-shadow: 0 0 20px rgba(9, 105, 218, 0.5);\n  transition: all 0.3s ease;\n}\n\n.coin-rotate:hover {\n  box-shadow: 0 0 30px rgba(9, 105, 218, 0.8);\n  transform: scale(1.05);\n}\n\n.coin-container {\n  perspective: 1000px;\n  border-radius: 50%;\n  overflow: hidden;\n  position: relative;\n}\n\n.coin-container::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, rgba(9, 105, 218, 0.2), transparent);\n  pointer-events: none;\n}\n\n@keyframes rotate3d {\n  0% {\n    transform: rotateY(0deg);\n  }\n  100% {\n    transform: rotateY(360deg);\n  }\n}\n"]}