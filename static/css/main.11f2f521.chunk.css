:root{--primary-color:#0969da;--secondary-color:#1f222e;--accent-color:#f8d866;--dark-color:#121212;--light-color:#f5f5f5}*{box-sizing:border-box;margin:0;padding:0}body{font-family:"Inter",sans-serif;background-color:var(--dark-color);color:var(--light-color);line-height:1.6;overflow-x:hidden}code,pre{font-family:"Fira Code",monospace}a{color:inherit;text-decoration:none}ul{list-style:none}button,input,textarea{font-family:inherit}.container{width:100%;max-width:1200px;margin:0 auto;padding:0 2rem}.section{padding:6rem 0}.gradient-text{background:linear-gradient(90deg,var(--primary-color),var(--accent-color));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.glass-effect{background:hsla(0,0%,100%,.05);backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid hsla(0,0%,100%,.1);border-radius:10px}@media (max-width:768px){.section{padding:4rem 0}}.app{min-height:100vh;display:flex;flex-direction:column}.main-content{flex:1 1}.canvas-container{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:-1}.btn{display:inline-block;padding:.8rem 1.5rem;border-radius:5px;font-weight:600;cursor:pointer;transition:all .3s ease}.btn-primary{background-color:var(--primary-color);color:#fff;border:none}.btn-primary:hover{background-color:#0859b8;transform:translateY(-2px)}.btn-outline{background-color:initial;color:var(--primary-color);border:1px solid var(--primary-color)}.btn-outline:hover{background-color:var(--primary-color);color:#fff;transform:translateY(-2px)}.flex{display:flex}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.text-center{text-align:center}.grid{display:grid}.grid-cols-1{grid-template-columns:repeat(1,1fr)}@media (min-width:768px){.grid-cols-md-2{grid-template-columns:repeat(2,1fr)}}@media (min-width:992px){.grid-cols-lg-3{grid-template-columns:repeat(3,1fr)}}.coin-rotate{animation:rotate3d 8s linear infinite;transform-style:preserve-3d;border-radius:50%;box-shadow:0 0 20px rgba(0,0,0,.3)}.coin-container{perspective:1000px;border-radius:50%;overflow:hidden}@keyframes rotate3d{0%{transform:rotateY(0deg)}to{transform:rotateY(1turn)}}
/*# sourceMappingURL=main.11f2f521.chunk.css.map */