{"version": 3, "sources": ["components/Navbar.js", "components/Footer.js", "components/StarfieldAnimation.js", "components/ProjectCard.js", "pages/Home.js", "pages/About.js", "pages/Projects.js", "pages/Skills.js", "pages/Contact.js", "components/AIChatbot.js", "App.js", "index.js"], "names": ["NavbarContainer", "styled", "header", "props", "scrolled", "Nav<PERSON><PERSON><PERSON>", "div", "Logo", "motion", "NavLinks", "nav", "NavLink", "a", "MobileMenuButton", "button", "MobileMenu", "MobileNavLink", "<PERSON><PERSON><PERSON>", "isOpen", "setIsOpen", "useState", "setScrolled", "useEffect", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navLinks", "name", "path", "_jsxs", "children", "_jsx", "Link", "to", "className", "whileHover", "scale", "whileTap", "map", "link", "y", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "initial", "opacity", "animate", "exit", "transition", "duration", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FooterTop", "<PERSON><PERSON><PERSON><PERSON>", "FooterTagline", "p", "SocialLinks", "SocialLink", "FooterBottom", "Copyright", "FooterLinks", "FooterLink", "Footer", "currentYear", "Date", "getFullYear", "socialLinks", "url", "icon", "fillRule", "clipRule", "index", "href", "target", "rel", "<PERSON><PERSON>", "canvas", "StarfieldAnimation", "canvasRef", "useRef", "current", "ctx", "getContext", "animationFrameId", "setCanvasDimensions", "innerWidth", "innerHeight", "stars", "max<PERSON><PERSON><PERSON>", "i", "push", "x", "Math", "random", "z", "color", "fillStyle", "fillRect", "centerX", "centerY", "star", "radius", "beginPath", "arc", "PI", "requestAnimationFrame", "cancelAnimationFrame", "ref", "Card", "<PERSON><PERSON><PERSON><PERSON>", "ProjectImage", "hovered", "ProjectInitial", "ProjectPreview", "ProjectTitle", "h3", "ProjectDescription", "TechStack", "TechTag", "span", "CardLinks", "CardLink", "ProjectCard", "_ref", "project", "setHovered", "cardRef", "useMotionValue", "springX", "useSpring", "stiffness", "damping", "springY", "rotateX", "useTransform", "rotateY", "style", "onMouseMove", "e", "rect", "getBoundingClientRect", "xValue", "clientX", "left", "yValue", "clientY", "top", "set", "onMouseEnter", "onMouseLeave", "handleMouseLeave", "title", "char<PERSON>t", "description", "technologies", "tech", "github", "slug", "as", "HomeContainer", "HeroSection", "section", "Hero<PERSON><PERSON><PERSON>", "HeroText", "<PERSON><PERSON><PERSON><PERSON>", "h1", "HeroSubtitle", "h2", "HeroDescription", "ButtonGroup", "HeroVisual", "Section", "SectionTitle", "SectionSubtitle", "Divider", "ProjectsGrid", "ViewAllButton", "CallToAction", "CTAContent", "CTATitle", "CTADescription", "featuredProjects", "id", "Home", "delay", "borderRadius", "overflow", "display", "alignItems", "justifyContent", "position", "src", "alt", "objectFit", "objectPosition", "AboutC<PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "Page<PERSON><PERSON>le", "PageDescription", "SectionContent", "Column", "right", "Paragraph", "InfoCard", "InfoTitle", "<PERSON>fo<PERSON><PERSON>nt", "SkillsContainer", "SkillCategory", "CategoryTitle", "SkillsList", "ul", "SkillItem", "li", "About", "skills", "technical", "soft", "marginBottom", "boxShadow", "margin", "textAlign", "skill", "ProjectsContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FilterButton", "active", "EmptyState", "EmptyTitle", "EmptyDescription", "allProjects", "category", "Projects", "filter", "setFilter", "filteredProjects", "length", "SkillsGrid", "columns", "SkillCard", "SkillCategoryTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SkillName", "SkillLevel", "<PERSON><PERSON><PERSON><PERSON>", "SkillProgress", "level", "SoftSkillCard", "SoftSkillTitle", "SoftSkillDescription", "LearningSection", "LearningCard", "LearningTitle", "LearningList", "LearningItem", "Skills", "learning", "future", "categoryIndex", "skillIndex", "item", "points", "x1", "y1", "x2", "y2", "ContactContainer", "ContactSection", "ContactContent", "ContactInfo", "ContactForm", "InfoItem", "IconWrapper", "InfoLabel", "InfoText", "InfoLink", "FormCard", "FormTitle", "Form", "form", "FormRow", "FormGroup", "FormLabel", "label", "FormInput", "input", "FormTextarea", "textarea", "SubmitButton", "SuccessMessage", "Contact", "formData", "setFormData", "email", "subject", "message", "isSubmitting", "setIsSubmitting", "submitSuccess", "setSubmitSuccess", "handleChange", "value", "prev", "cx", "cy", "r", "marginTop", "gap", "flexWrap", "marginRight", "onSubmit", "preventDefault", "setTimeout", "htmlFor", "type", "onChange", "placeholder", "required", "disabled", "_Fragment", "ChatbotButton", "ChatWindow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChatTitle", "ChatSubtitle", "ChatMessages", "MessageBubble", "sender", "ChatForm", "ChatInput", "SendButton", "AIChatbot", "messages", "setMessages", "text", "setInput", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "scrollIntoView", "behavior", "getResponse", "lowerMessage", "toLowerCase", "includes", "AnimatePresence", "trim", "userMessage", "botResponse", "prevMessages", "App", "Router", "exitBeforeEnter", "Switch", "Route", "exact", "component", "ReactDOM", "render", "React", "StrictMode", "document", "getElementById"], "mappings": "8PAKA,MAAMA,EAAkBC,IAAOC,MAAM;;;;;;;aAOxBC,GAASA,EAAMC,SAAW,WAAa;gBACpCD,GAASA,EAAMC,SAAW,wBAA0B;qBAC/CD,GAASA,EAAMC,SAAW,aAAe;gBAC9CD,GAASA,EAAMC,SAAW,gCAAkC;EAGtEC,EAAaJ,IAAOK,GAAG;;;;;;;EASvBC,EAAON,YAAOO,IAAOF,IAAI;;;;EAMzBG,EAAWR,IAAOS,GAAG;;;;;;;EASrBC,EAAUV,YAAOO,IAAOI,EAAE;;;;;;;;EAU1BC,EAAmBZ,IAAOa,MAAM;;;;;;;;;;;EAahCC,EAAad,YAAOO,IAAOF,IAAI;;;;;;;;;;;;EAc/BU,EAAgBf,IAAOW,CAAC;;;;;;;;;EA+FfK,MApFAA,KACb,MAAOC,EAAQC,GAAaC,oBAAS,IAC9BhB,EAAUiB,GAAeD,oBAAS,GAEzCE,qBAAU,KACR,MAAMC,EAAeA,KACfC,OAAOC,QAAU,GACnBJ,GAAY,GAEZA,GAAY,EACd,EAIF,OADAG,OAAOE,iBAAiB,SAAUH,GAC3B,IAAMC,OAAOG,oBAAoB,SAAUJ,EAAa,GAC9D,IAEH,MAAMK,EAAW,CACf,CAAEC,KAAM,OAAQC,KAAM,KACtB,CAAED,KAAM,QAASC,KAAM,UACvB,CAAED,KAAM,WAAYC,KAAM,aAC1B,CAAED,KAAM,SAAUC,KAAM,WACxB,CAAED,KAAM,UAAWC,KAAM,aAG3B,OACEC,eAAC/B,EAAe,CAACI,SAAUA,EAAS4B,SAAA,CAClCD,eAAC1B,EAAU,CAAA2B,SAAA,CACTC,cAACC,IAAI,CAACC,GAAG,IAAGH,SACVC,cAAC1B,EAAI,CACH6B,UAAU,gBACVC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAON,SAC3B,eAKHC,cAACxB,EAAQ,CAAAuB,SACNJ,EAASY,KAAKC,GACbR,cAACC,IAAI,CAAiBC,GAAIM,EAAKX,KAAKE,SAClCC,cAACtB,EAAO,CACN0B,WAAY,CAAEK,GAAI,GAClBH,SAAU,CAAEG,EAAG,GAAIV,SAElBS,EAAKZ,QALCY,EAAKZ,UAWpBI,cAACpB,EAAgB,CAAC8B,QAASA,IAAMxB,GAAWD,GAAQc,SACjDd,EACCe,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BhB,SAC5FC,cAAA,QAAMgB,EAAE,uBAAuBC,OAAO,eAAeC,YAAY,IAAIC,cAAc,QAAQC,eAAe,YAG5GpB,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BhB,SAC5FC,cAAA,QAAMgB,EAAE,0BAA0BC,OAAO,eAAeC,YAAY,IAAIC,cAAc,QAAQC,eAAe,iBAMpHnC,GACCe,cAAClB,EAAU,CACTuC,QAAS,CAAEC,QAAS,EAAGb,GAAI,IAC3Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1Be,KAAM,CAAEF,QAAS,EAAGb,GAAI,IACxBgB,WAAY,CAAEC,SAAU,IAAM3B,SAE7BJ,EAASY,KAAKC,GACbR,cAACC,IAAI,CAAiBC,GAAIM,EAAKX,KAAKE,SAClCC,cAACjB,EAAa,CAAC2B,QAASA,IAAMxB,GAAU,GAAOa,SAC5CS,EAAKZ,QAFCY,EAAKZ,YAQN,ECrKtB,MAAM+B,EAAkB3D,IAAO4D,MAAM;;;;;EAO/BC,EAAgB7D,IAAOK,GAAG;;;;EAM1ByD,EAAY9D,IAAOK,GAAG;;;;;;;;;;EAYtB0D,EAAa/D,IAAOK,GAAG;;;;;;EAQvB2D,EAAgBhE,IAAOiE,CAAC;;;;EAMxBC,EAAclE,IAAOK,GAAG;;;EAKxB8D,EAAanE,YAAOO,IAAOI,EAAE;;;;;;;;;;;;;;EAgB7ByD,EAAepE,IAAOK,GAAG;;;;;;;;;;;EAazBgE,EAAYrE,IAAOiE,CAAC;;;;;;;;EAUpBK,EAActE,IAAOK,GAAG;;;EAKxBkE,EAAavE,IAAOW,CAAC;;;;;;;;EA+FZ6D,MArFAA,KACb,MAAMC,GAAc,IAAIC,MAAOC,cAEzBC,EAAc,CAClB,CACEhD,KAAM,SACNiD,IAAK,mCACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAM+C,SAAS,UAAU/B,EAAE,mtBAAmtBgC,SAAS,eAI7vB,CACEpD,KAAM,WACNiD,IAAK,4DACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAMgB,EAAE,0fAId,CACEpB,KAAM,UACNiD,IAAK,0BACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAMgB,EAAE,+aAId,CACEpB,KAAM,WACNiD,IAAK,0DACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAM+C,SAAS,UAAU/B,EAAE,yQAAyQgC,SAAS,gBAMrT,OACEhD,cAAC2B,EAAe,CAAA5B,SACdD,eAAC+B,EAAa,CAAA9B,SAAA,CACZD,eAACgC,EAAS,CAAA/B,SAAA,CACRD,eAACiC,EAAU,CAAAhC,SAAA,CACTC,cAACC,IAAI,CAACC,GAAG,IAAGH,SACVC,cAAA,MAAIG,UAAU,gBAAeJ,SAAC,eAEhCC,cAACgC,EAAa,CAAAjC,SAAC,wCAGjBC,cAACkC,EAAW,CAAAnC,SACT6C,EAAYrC,KAAI,CAACC,EAAMyC,IACtBjD,cAACmC,EAAU,CAETe,KAAM1C,EAAKqC,IACXM,OAAO,SACPC,IAAI,sBACJ,aAAY5C,EAAKZ,KACjBQ,WAAY,CAAEK,GAAI,GAClBH,SAAU,CAAEG,EAAG,GAAIV,SAElBS,EAAKsC,MARDG,UAcbnD,eAACsC,EAAY,CAAArC,SAAA,CACXD,eAACuC,EAAS,CAAAtC,SAAA,CAAC,QACN0C,EAAY,qDAGjB3C,eAACwC,EAAW,CAAAvC,SAAA,CACVC,cAACuC,EAAU,CAACW,KAAK,WAAUnD,SAAC,mBAC5BC,cAACuC,EAAU,CAACW,KAAK,SAAQnD,SAAC,+BAIhB,ECrLtB,MAAMsD,EAASrF,IAAOsF,MAAM;;;;;;;EA6FbC,MApFYA,KACzB,MAAMC,EAAYC,iBAAO,MAgFzB,OA9EApE,qBAAU,KACR,MAAMiE,EAASE,EAAUE,QACnBC,EAAML,EAAOM,WAAW,MAC9B,IAAIC,EAGJ,MAAMC,EAAsBA,KAC1BR,EAAO3C,MAAQpB,OAAOwE,WACtBT,EAAO1C,OAASrB,OAAOyE,WAAW,EAGpCF,IACAvE,OAAOE,iBAAiB,SAAUqE,GAGlC,MAAMG,EAAQ,GAERC,EAAW,IAGjB,IAAK,IAAIC,EAAI,EAAGA,EAJE,IAIaA,IAC7BF,EAAMG,KAAK,CACTC,EAAGC,KAAKC,SAAWjB,EAAO3C,MAAQ2C,EAAO3C,MAAQ,EACjDF,EAAG6D,KAAKC,SAAWjB,EAAO1C,OAAS0C,EAAO1C,OAAS,EACnD4D,EAAGF,KAAKC,SAAWL,EACnBO,MAAO,QAAwB,IAAhBH,KAAKC,SAAiB,QAAwB,IAAhBD,KAAKC,SAAiB,QAAwB,IAAhBD,KAAKC,SAAiB,QAAwB,GAAhBD,KAAKC,SAAiB,QAKnI,MAAMhD,EAAUA,KACdoC,EAAIe,UAAY,wBAChBf,EAAIgB,SAAS,EAAG,EAAGrB,EAAO3C,MAAO2C,EAAO1C,QAExC,MAAMgE,EAAUtB,EAAO3C,MAAQ,EACzBkE,EAAUvB,EAAO1C,OAAS,EAGhC,IAAK,IAAIuD,EAAI,EAAGA,EAtBA,IAsBeA,IAAK,CAClC,MAAMW,EAAOb,EAAME,GAGnBW,EAAKN,GAAK,GAGNM,EAAKN,GAAK,IACZM,EAAKT,EAAIC,KAAKC,SAAWjB,EAAO3C,MAAQiE,EACxCE,EAAKrE,EAAI6D,KAAKC,SAAWjB,EAAO1C,OAASiE,EACzCC,EAAKN,EAAIN,GAIX,MAAM7D,EAAQ6D,GAAYA,EAAWY,EAAKN,GACpCH,EAAIO,EAAUE,EAAKT,EAAIhE,EACvBI,EAAIoE,EAAUC,EAAKrE,EAAIJ,EAGvB0E,EAAiB,IAAR1E,EAGfsD,EAAIqB,YACJrB,EAAIsB,IAAIZ,EAAG5D,EAAGsE,EAAQ,EAAG,EAAIT,KAAKY,IAClCvB,EAAIe,UAAYI,EAAKL,MACrBd,EAAI7C,MACN,CAEA+C,EAAmBsB,sBAAsB5D,EAAQ,EAMnD,OAHAA,IAGO,KACLhC,OAAOG,oBAAoB,SAAUoE,GACrCsB,qBAAqBvB,EAAiB,CACvC,GACA,IAEI7D,cAACqD,EAAM,CAACgC,IAAK7B,GAAa,E,wBCxFnC,MAAM8B,EAAOtH,YAAOO,IAAOF,IAAI;;;;;;;;;;EAYzBkH,EAAcvH,IAAOK,GAAG;;;;;EAOxBmH,EAAexH,IAAOK,GAAG;;;;;;;;;;eAUhBH,GAASA,EAAMuH,QAAU,cAAgB;EAGlDC,EAAiB1H,IAAOK,GAAG;;;;EAM3BsH,EAAiB3H,IAAOK,GAAG;;;;EAM3BuH,EAAe5H,IAAO6H,EAAE;;;;EAMxBC,EAAqB9H,IAAOiE,CAAC;;;;;EAO7B8D,EAAY/H,IAAOK,GAAG;;;;;EAOtB2H,EAAUhI,IAAOiI,IAAI;;;;;;;EASrBC,EAAYlI,IAAOK,GAAG;;;;EAMtB8H,EAAWnI,IAAOW,CAAC;;;;;;;;;;;;EA8GVyH,MAhGKC,IAAkB,IAAjB,QAAEC,GAASD,EAC9B,MAAOZ,EAASc,GAAcpH,oBAAS,GACjCqH,EAAU/C,iBAAO,MAGjBY,EAAIoC,YAAe,GACnBhG,EAAIgG,YAAe,GAGnBC,EAAUC,YAAUtC,EAAG,CAAEuC,UAAW,IAAKC,QAAS,KAClDC,EAAUH,YAAUlG,EAAG,CAAEmG,UAAW,IAAKC,QAAS,KAGlDE,EAAUC,YAAaF,EAAS,EAAE,GAAK,IAAM,CAAC,IAAK,KACnDG,EAAUD,YAAaN,EAAS,EAAE,GAAK,IAAM,EAAE,GAAI,KAyBzD,OACE1G,cAACsF,EAAI,CACHD,IAAKmB,EACLpG,WAAY,CAAEC,MAAO,MACrB6G,MAAO,CACLH,QAASA,EACTE,QAASA,GAEXE,YA9BqBC,IACvB,IAAKZ,EAAQ9C,QAAS,OAEtB,MAAM2D,EAAOb,EAAQ9C,QAAQ4D,wBACvB3G,EAAQ0G,EAAK1G,MACbC,EAASyG,EAAKzG,OAGd2G,GAAUH,EAAEI,QAAUH,EAAKI,MAAQ9G,EAAQ,GAC3C+G,GAAUN,EAAEO,QAAUN,EAAKO,KAAOhH,EAAS,GAEjDyD,EAAEwD,IAAIN,GACN9G,EAAEoH,IAAIH,EAAO,EAmBXI,aAAcA,IAAMvB,GAAW,GAC/BwB,aAhBqBC,KACvB3D,EAAEwD,IAAI,GACNpH,EAAEoH,IAAI,GACNtB,GAAW,EAAM,EAagBxG,SAE/BD,eAACyF,EAAW,CAAAxF,SAAA,CACVC,cAACwF,EAAY,CAACC,QAASA,EAAQ1F,SAC7BD,eAAA,OAAKK,UAAU,cAAaJ,SAAA,CAC1BC,cAAC0F,EAAc,CAAA3F,SAAEuG,EAAQ2B,MAAMC,OAAO,KACtClI,cAAC2F,EAAc,CAAA5F,SAAC,yBAIpBC,cAAC4F,EAAY,CAACzF,UAAU,gBAAeJ,SAAEuG,EAAQ2B,QACjDjI,cAAC8F,EAAkB,CAAA/F,SAAEuG,EAAQ6B,cAE7BnI,cAAC+F,EAAS,CAAAhG,SACPuG,EAAQ8B,aAAa7H,KAAI,CAAC8H,EAAMpF,IAC/BjD,cAACgG,EAAO,CAAAjG,SAAcsI,GAARpF,OAIlBnD,eAACoG,EAAS,CAAAnG,SAAA,CACPuG,EAAQgC,QACPxI,eAACqG,EAAQ,CACPjD,KAAMoD,EAAQgC,OACdnF,OAAO,SACPC,IAAI,sBAAqBrD,SAAA,CAEzBC,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAM+C,SAAS,UAAU/B,EAAE,mtBAAmtBgC,SAAS,cACnvB,UAKVhD,cAACC,IAAI,CAACC,GAAI,aAAaoG,EAAQiC,OAAOxI,SACpCD,eAACqG,EAAQ,CAACqC,GAAG,OAAMzI,SAAA,CAAC,eAElBC,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,eAAeJ,QAAQ,YAAWd,SAC/EC,cAAA,QAAMmB,cAAc,QAAQC,eAAe,QAAQF,YAAY,IAAIF,EAAE,2CAM1E,ECvLX,MAAMyH,EAAgBzK,IAAOK,GAAG;;EAI1BqK,EAAc1K,IAAO2K,OAAO;;;;;;EAQ5BC,EAAc5K,IAAOK,GAAG;;;;;;;;;;;EAaxBwK,GAAW7K,YAAOO,IAAOF,IAAI;;;;;;;;EAU7ByK,GAAY9K,IAAO+K,EAAE;;;;;;;;EAUrBC,GAAehL,IAAOiL,EAAE;;;;;;;;;EAWxBC,GAAkBlL,IAAOiE,CAAC;;;;;EAO1BkH,GAAcnL,IAAOK,GAAG;;;;EAMxB+K,GAAapL,YAAOO,IAAOF,IAAI;;;;;;;EAS/BgL,GAAUrL,IAAO2K,OAAO;;EAIxBW,GAAetL,IAAOiL,EAAE;;;;;EAOxBM,GAAkBvL,IAAOiE,CAAC;;;;;;EAQ1BuH,GAAUxL,IAAOK,GAAG;;;;;EAOpBoL,GAAezL,IAAOK,GAAG;;;;;;;;;;;;;;;EAiBzBqL,GAAgB1L,IAAOK,GAAG;;;EAK1BsL,GAAe3L,IAAO2K,OAAO;;;;EAM7BiB,GAAa5L,IAAOK,GAAG;;;;EAMvBwL,GAAW7L,IAAOiL,EAAE;;;;EAMpBa,GAAiB9L,IAAOiE,CAAC;;;;EAOzB8H,GAAmB,CACvB,CACEC,GAAI,EACJ/B,MAAO,YACPM,KAAM,aACNJ,YAAa,qHACbC,aAAc,CAAC,SAAU,OAAQ,kBAAmB,YACpDE,OAAQ,wDAEV,CACE0B,GAAI,EACJ/B,MAAO,gCACPM,KAAM,sBACNJ,YAAa,8HACbC,aAAc,CAAC,SAAU,OAAQ,oBACjCE,OAAQ,kEAEV,CACE0B,GAAI,EACJ/B,MAAO,UACPM,KAAM,UACNJ,YAAa,yHACbC,aAAc,CAAC,SAAU,2BAA4B,UACrDE,OAAQ,6CA2IG2B,OAvIFA,IAETnK,eAAC2I,EAAa,CAAA1I,SAAA,CACZC,cAACuD,EAAkB,IAEnBvD,cAAC0I,EAAW,CAAA3I,SACVD,eAAC8I,EAAW,CAAA7I,SAAA,CACVD,eAAC+I,GAAQ,CACPxH,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BD,eAACgJ,GAAS,CAAA/I,SAAA,CAAC,WACDC,cAAA,QAAMG,UAAU,gBAAeJ,SAAC,sBAE1CC,cAACgJ,GAAY,CAAAjJ,SAAC,qCAGdC,cAACkJ,GAAe,CAAAnJ,SAAC,6HAGjBD,eAACqJ,GAAW,CAAApJ,SAAA,CACVC,cAACC,IAAI,CAACC,GAAG,YAAWH,SAClBC,cAACzB,IAAOM,OAAM,CACZsB,UAAU,kBACVC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAON,SAC3B,mBAIHC,cAACC,IAAI,CAACC,GAAG,WAAUH,SACjBC,cAACzB,IAAOM,OAAM,CACZsB,UAAU,kBACVC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAON,SAC3B,uBAOPC,cAACoJ,GAAU,CACT/H,QAAS,CAAEC,QAAS,EAAGjB,MAAO,IAC9BkB,QAAS,CAAED,QAAS,EAAGjB,MAAO,GAC9BoB,WAAY,CAAEC,SAAU,GAAKwI,MAAO,IAAMnK,SAE1CC,cAAA,OAAKG,UAAU,8BAA8B+G,MAAO,CAClDvG,MAAO,OACPC,OAAQ,OACRuJ,aAAc,MACdC,SAAU,SACVC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBC,SAAU,YACVzK,SACAC,cAAA,OACEyK,IAA8B,wCAC9BC,IAAI,2BACJvK,UAAU,cACV+G,MAAO,CACLvG,MAAO,OACPC,OAAQ,OACR+J,UAAW,QACXC,eAAgB,SAChBT,aAAc,kBAQ1BrK,eAACuJ,GAAO,CAAAtJ,SAAA,CACNC,cAACsJ,GAAY,CAAAvJ,SAAC,sBACdC,cAACwJ,GAAO,IACRxJ,cAACuJ,GAAe,CAAAxJ,SAAC,qHAIjBC,cAACyJ,GAAY,CAAA1J,SACVgK,GAAiBxJ,KAAI,CAAC+F,EAASrD,IAC9BjD,cAACzB,IAAOF,IAAG,CAETgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,GAAKwI,MAAe,GAARjH,GAAclD,SAElDC,cAACoG,EAAW,CAACE,QAASA,KALjBA,EAAQ0D,QAUnBhK,cAAC0J,GAAa,CAAA3J,SACZC,cAACC,IAAI,CAACC,GAAG,YAAWH,SAClBC,cAACzB,IAAOM,OAAM,CACZsB,UAAU,kBACVC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAON,SAC3B,6BAOPC,cAAC2J,GAAY,CAAA5J,SACXC,cAAC4J,GAAU,CAAA7J,SACTD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAAC6J,GAAQ,CAAA9J,SAAC,wBACVC,cAAC8J,GAAc,CAAA/J,SAAC,6FAGhBC,cAACC,IAAI,CAACC,GAAG,WAAUH,SACjBC,cAACzB,IAAOM,OAAM,CACZsB,UAAU,kBACVC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAON,SAC3B,6BChTf,MAAM8K,GAAiB7M,IAAOK,GAAG;;;EAK3ByM,GAAa9M,IAAOK,GAAG;;;EAKvB0M,GAAY/M,IAAO+K,EAAE;;;;EAMrBS,GAAUxL,IAAOK,GAAG;;;;;EAOpB2M,GAAkBhN,IAAOiE,CAAC;;;;;EAO1BoH,GAAUrL,IAAO2K,OAAO;;;;;;EAQxBsC,GAAiBjN,IAAOK,GAAG;;;;;;;;;;EAY3B6M,GAASlN,IAAOK,GAAG;;;;;;MAMnBH,GAASA,EAAMiN,MAAQ,qBAAuB;;EAI9C7B,GAAetL,IAAOiL,EAAE;;;;EAMxBmC,GAAYpN,IAAOiE,CAAC;;;;EAMpBoJ,GAAWrN,IAAOK,GAAG;;;;;;;EASrBiN,GAAYtN,IAAO6H,EAAE;;;;;EAOrB0F,GAAcvN,IAAOiE,CAAC;;EAItBuJ,GAAkBxN,IAAOK,GAAG;;;;;;;;EAU5BoN,GAAgBzN,IAAOK,GAAG;;;;;;EAQ1BqN,GAAgB1N,IAAO6H,EAAE;;;;;EAOzB8F,GAAa3N,IAAO4N,EAAE;;EAItBC,GAAY7N,IAAO8N,EAAE;;;;;;;;;;;;;;EAmKZC,OAnJDA,KAEZ,MAAMC,EAAS,CACbC,UAAW,CACT,SAAU,aAAc,QAAS,SAAU,QAC3C,aAAc,mBAAoB,gBAClC,QAAS,MAAO,YAElBC,KAAM,CACJ,kBAAmB,gBAAiB,WACpC,eAAgB,kBAAmB,sBAIvC,OACEpM,eAAC+K,GAAc,CAAA9K,SAAA,CACbC,cAACuD,EAAkB,IAEnBvD,cAAC8K,GAAU,CAAA/K,SACTD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAAC+K,GAAS,CAAAhL,SAAC,aACXC,cAACwJ,GAAO,IACRxJ,cAACgL,GAAe,CAAAjL,SAAC,sFAMrBC,cAACqJ,GAAO,CAAAtJ,SACND,eAACmL,GAAc,CAAAlL,SAAA,CACbC,cAACkL,GAAM,CAAAnL,SACLD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAG+C,GAAI,IAC3B9C,QAAS,CAAED,QAAS,EAAG+C,EAAG,GAC1B5C,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAACsJ,GAAY,CAACnJ,UAAU,gBAAeJ,SAAC,aACxCC,cAACoL,GAAS,CAAArL,SAAC,qPAGXC,cAACoL,GAAS,CAAArL,SAAC,4NAGXC,cAACoL,GAAS,CAAArL,SAAC,kRAGXC,cAACoL,GAAS,CAAArL,SAAC,yKAMfC,cAACkL,GAAM,CAACC,OAAK,EAAApL,SACXD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAG+C,EAAG,IAC1B9C,QAAS,CAAED,QAAS,EAAG+C,EAAG,GAC1B5C,WAAY,CAAEC,SAAU,GAAKwI,MAAO,IAAMnK,SAAA,CAE1CC,cAAA,OAAKG,UAAU,8BAA8B+G,MAAO,CAClDiF,aAAc,OACdhC,aAAc,MACdC,SAAU,SACVgC,UAAW,iCACXzL,MAAO,QACPC,OAAQ,QACRyL,OAAQ,eACRtM,SACAC,cAAA,OACEyK,IAA8B,wCAC9BC,IAAI,2BACJvK,UAAU,cACV+G,MAAO,CACLvG,MAAO,OACPC,OAAQ,OACR+J,UAAW,QACXC,eAAgB,SAChBT,aAAc,WAKpBnK,cAACsJ,GAAY,CAACnJ,UAAU,gBAAeJ,SAAC,gBAExCD,eAACuL,GAAQ,CAAAtL,SAAA,CACPC,cAACsL,GAAS,CAAAvL,SAAC,aACXC,cAACuL,GAAW,CAAAxL,SAAC,kCAGfD,eAACuL,GAAQ,CAAAtL,SAAA,CACPC,cAACsL,GAAS,CAAAvL,SAAC,gBACXC,cAACuL,GAAW,CAAAxL,SAAC,uDAGfD,eAACuL,GAAQ,CAAAtL,SAAA,CACPC,cAACsL,GAAS,CAAAvL,SAAC,cACXC,cAACuL,GAAW,CAAAxL,SAAC,uCAGfD,eAACuL,GAAQ,CAAAtL,SAAA,CACPC,cAACsL,GAAS,CAAAvL,SAAC,cACXC,cAACuL,GAAW,CAAAxL,SAAC,+DAOvBC,cAACqJ,GAAO,CAAAtJ,SACNC,cAACiL,GAAc,CAAAlL,SACbD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IACxBwF,MAAO,CAAEvG,MAAO,QAASZ,SAAA,CAEzBC,cAACsJ,GAAY,CAACnJ,UAAU,gBAAgB+G,MAAO,CAAEoF,UAAW,SAAUH,aAAc,QAASpM,SAAC,cAE9FD,eAAC0L,GAAe,CAAAzL,SAAA,CACdD,eAAC2L,GAAa,CAAA1L,SAAA,CACZC,cAAC0L,GAAa,CAAA3L,SAAC,qBACfC,cAAC2L,GAAU,CAAA5L,SACRiM,EAAOC,UAAU1L,KAAI,CAACgM,EAAOtJ,IAC5BjD,cAAC6L,GAAS,CAAA9L,SAAcwM,GAARtJ,UAKtBnD,eAAC2L,GAAa,CAAA1L,SAAA,CACZC,cAAC0L,GAAa,CAAA3L,SAAC,gBACfC,cAAC2L,GAAU,CAAA5L,SACRiM,EAAOE,KAAK3L,KAAI,CAACgM,EAAOtJ,IACvBjD,cAAC6L,GAAS,CAAA9L,SAAcwM,GAARtJ,sBAQf,ECvRrB,MAAMuJ,GAAoBxO,IAAOK,GAAG;;;EAK9ByM,GAAa9M,IAAOK,GAAG;;;EAKvB0M,GAAY/M,IAAO+K,EAAE;;;;EAMrBS,GAAUxL,IAAOK,GAAG;;;;;EAOpB2M,GAAkBhN,IAAOiE,CAAC;;;;;EAO1BwK,GAAkBzO,IAAOK,GAAG;;;;;;;EAS5BqO,GAAe1O,IAAOa,MAAM;;;;;;;;;sBASZX,GAASA,EAAMyO,OAAS,uBAAyB;WAC5DzO,GAASA,EAAMyO,OAAS,QAAU;;;wBAGrBzO,GAASA,EAAMyO,OAAS,uBAAyB;;EAInElD,GAAezL,IAAOK,GAAG;;;;;;;;;;;;;;;EAiBzBuO,GAAa5O,IAAOK,GAAG;;;;EAMvBwO,GAAa7O,IAAO6H,EAAE;;;;;EAOtBiH,GAAmB9O,IAAOiE,CAAC;;;;EAO3B8K,GAAc,CAClB,CACE/C,GAAI,EACJ/B,MAAO,YACPM,KAAM,aACNJ,YAAa,8OACbC,aAAc,CAAC,SAAU,OAAQ,kBAAmB,WAAY,oBAChE4E,SAAU,QACV1E,OAAQ,wDAEV,CACE0B,GAAI,EACJ/B,MAAO,gCACPM,KAAM,sBACNJ,YAAa,wMACbC,aAAc,CAAC,SAAU,OAAQ,mBAAoB,UACrD4E,SAAU,QACV1E,OAAQ,kEAEV,CACE0B,GAAI,EACJ/B,MAAO,UACPM,KAAM,UACNJ,YAAa,qLACbC,aAAc,CAAC,SAAU,2BAA4B,SAAU,cAC/D4E,SAAU,kBACV1E,OAAQ,4CAEV,CACE0B,GAAI,EACJ/B,MAAO,0BACPM,KAAM,uBACNJ,YAAa,kHACbC,aAAc,CAAC,OAAQ,MAAO,cAC9B4E,SAAU,kBACV1E,OAAQ,yDAEV,CACE0B,GAAI,EACJ/B,MAAO,oBACPM,KAAM,qBACNJ,YAAa,kNACbC,aAAc,CAAC,aAAc,WAAY,WAAY,WACrD4E,SAAU,aACV1E,OAAQ,sDAEV,CACE0B,GAAI,EACJ/B,MAAO,YACPM,KAAM,YACNJ,YAAa,qFACbC,aAAc,CAAC,aAAc,aAAc,SAC3C4E,SAAU,aACV1E,OAAQ,+CAgFG2E,OA5EEA,KACf,MAAOC,EAAQC,GAAahO,mBAAS,OAI/BiO,EAA8B,QAAXF,EACrBH,GACAA,GAAYG,QAAO5G,GAAWA,EAAQ0G,WAAaE,IAEvD,OACEpN,eAAC0M,GAAiB,CAAAzM,SAAA,CAChBC,cAACuD,EAAkB,IAEnBvD,cAAC8K,GAAU,CAAA/K,SACTD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAAC+K,GAAS,CAAAhL,SAAC,gBACXC,cAACwJ,GAAO,IACRxJ,cAACgL,GAAe,CAAAjL,SAAC,wGAMrBC,cAACyM,GAAe,CAAA1M,SAzBD,CAAC,MAAO,QAAS,kBAAmB,cA0BrCQ,KAAI,CAACyM,EAAU/J,IACzBjD,cAACzB,IAAOF,IAAG,CAETgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,GAAKwI,MAAe,GAARjH,GAAclD,SAElDC,cAAC0M,GAAY,CACXC,OAAQO,IAAWF,EACnBtM,QAASA,IAAMyM,EAAUH,GAAUjN,SAElCiN,KATE/J,OAeXjD,cAACyJ,GAAY,CAAA1J,SACVqN,EAAiBC,OAAS,EACzBD,EAAiB7M,KAAI,CAAC+F,EAASrD,IAC7BjD,cAACzB,IAAOF,IAAG,CAETgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,GAAKwI,MAAe,GAARjH,GAAclD,SAElDC,cAACoG,EAAW,CAACE,QAASA,KALjBA,EAAQ0D,MASjBhK,cAAC4M,GAAU,CAAA7M,SACTD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBG,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAAC6M,GAAU,CAAA9M,SAAC,sBACZC,cAAC8M,GAAgB,CAAA/M,SAAC,wFAOR,EC/NxB,MAAMyL,GAAkBxN,IAAOK,GAAG;;;EAK5ByM,GAAa9M,IAAOK,GAAG;;;EAKvB0M,GAAY/M,IAAO+K,EAAE;;;;EAMrBS,GAAUxL,IAAOK,GAAG;;;;;EAOpB2M,GAAkBhN,IAAOiE,CAAC;;;;;EAO1BoH,GAAUrL,IAAO2K,OAAO;;;;;;EAQxBsC,GAAiBjN,IAAOK,GAAG;;;;EAM3BiL,GAAetL,IAAOiL,EAAE;;;;;EAOxBqE,GAAatP,IAAOK,GAAG;;;;;;;;;;oCAUOH,GAASA,EAAMqP,SAAW;;EAIxDC,GAAYxP,IAAOK,GAAG;;;;;;;EAStBoP,GAAqBzP,IAAO6H,EAAE;;;;;EAO9BgG,GAAY7N,IAAOK,GAAG;;EAItBqP,GAAc1P,IAAOK,GAAG;;;;EAMxBsP,GAAY3P,IAAOiI,IAAI;;EAIvB2H,GAAa5P,IAAOiI,IAAI;;EAIxB4H,GAAW7P,IAAOK,GAAG;;;;;EAOrByP,GAAgB9P,IAAOK,GAAG;;gBAEhBH,GAASA,EAAMuG,OAAS;;WAE7BvG,GAASA,EAAM6P;;EAIpBC,GAAgBhQ,IAAOK,GAAG;;;;;;;EAS1B4P,GAAiBjQ,IAAO6H,EAAE;;;;;EAO1BqI,GAAuBlQ,IAAOiE,CAAC;;;;EAM/BkM,GAAkBnQ,IAAOK,GAAG;;;;;;;;EAU5B+P,GAAepQ,IAAOK,GAAG;;;;;;EAQzBgQ,GAAgBrQ,IAAO6H,EAAE;;;;;;;;EAUzByI,GAAetQ,IAAO4N,EAAE;;EAIxB2C,GAAevQ,IAAO8N,EAAE;;;;;;;;;;;EAoOf0C,OAvNAA,KAEb,MAwEMC,EAAW,CACf/K,QAAS,CACP,oCACA,8BACA,yBACA,qCAEFgL,OAAQ,CACN,oBACA,gCACA,yBACA,yCAIJ,OACE5O,eAAC0L,GAAe,CAAAzL,SAAA,CACdC,cAACuD,EAAkB,IAEnBvD,cAAC8K,GAAU,CAAA/K,SACTD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAAC+K,GAAS,CAAAhL,SAAC,cACXC,cAACwJ,GAAO,IACRxJ,cAACgL,GAAe,CAAAjL,SAAC,2FAMrBC,cAACqJ,GAAO,CAAAtJ,SACND,eAACmL,GAAc,CAAAlL,SAAA,CACbC,cAACsJ,GAAY,CAACnJ,UAAU,gBAAeJ,SAAC,qBAExCC,cAACsN,GAAU,CAAAvN,SA7GK,CACtB,CACEiN,SAAU,wBACVhB,OAAQ,CACN,CAAEpM,KAAM,SAAUmO,MAAO,GAAItJ,MAAO,WACpC,CAAE7E,KAAM,aAAcmO,MAAO,GAAItJ,MAAO,WACxC,CAAE7E,KAAM,OAAQmO,MAAO,GAAItJ,MAAO,WAClC,CAAE7E,KAAM,MAAOmO,MAAO,GAAItJ,MAAO,WACjC,CAAE7E,KAAM,MAAOmO,MAAO,GAAItJ,MAAO,aAGrC,CACEuI,SAAU,wBACVhB,OAAQ,CACN,CAAEpM,KAAM,aAAcmO,MAAO,GAAItJ,MAAO,WACxC,CAAE7E,KAAM,eAAgBmO,MAAO,GAAItJ,MAAO,WAC1C,CAAE7E,KAAM,8BAA+BmO,MAAO,GAAItJ,MAAO,WACzD,CAAE7E,KAAM,kBAAmBmO,MAAO,GAAItJ,MAAO,WAC7C,CAAE7E,KAAM,gBAAiBmO,MAAO,GAAItJ,MAAO,aAG/C,CACEuI,SAAU,kBACVhB,OAAQ,CACN,CAAEpM,KAAM,QAASmO,MAAO,GAAItJ,MAAO,WACnC,CAAE7E,KAAM,SAAUmO,MAAO,GAAItJ,MAAO,WACpC,CAAE7E,KAAM,QAASmO,MAAO,GAAItJ,MAAO,WACnC,CAAE7E,KAAM,eAAgBmO,MAAO,GAAItJ,MAAO,WAC1C,CAAE7E,KAAM,oBAAqBmO,MAAO,GAAItJ,MAAO,aAGnD,CACEuI,SAAU,uBACVhB,OAAQ,CACN,CAAEpM,KAAM,eAAgBmO,MAAO,GAAItJ,MAAO,WAC1C,CAAE7E,KAAM,QAASmO,MAAO,GAAItJ,MAAO,WACnC,CAAE7E,KAAM,SAAUmO,MAAO,GAAItJ,MAAO,WACpC,CAAE7E,KAAM,UAAWmO,MAAO,GAAItJ,MAAO,WACrC,CAAE7E,KAAM,QAASmO,MAAO,GAAItJ,MAAO,cAwEdlE,KAAI,CAACyM,EAAU2B,IAC9B3O,cAACzB,IAAOF,IAAG,CAETgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,GAAKwI,MAAuB,GAAhByE,GAAsB5O,SAE1DD,eAAC0N,GAAS,CAAAzN,SAAA,CACRC,cAACyN,GAAkB,CAAA1N,SAAEiN,EAASA,WAE7BA,EAAShB,OAAOzL,KAAI,CAACgM,EAAOqC,IAC3B9O,eAAC+L,GAAS,CAAA9L,SAAA,CACRD,eAAC4N,GAAW,CAAA3N,SAAA,CACVC,cAAC2N,GAAS,CAAA5N,SAAEwM,EAAM3M,OAClBE,eAAC8N,GAAU,CAAA7N,SAAA,CAAEwM,EAAMwB,MAAM,UAE3B/N,cAAC6N,GAAQ,CAAA9N,SACPC,cAAC8N,GAAa,CAACC,MAAOxB,EAAMwB,MAAOtJ,MAAO8H,EAAM9H,YANpCmK,SATfD,YA0Bf3O,cAACqJ,GAAO,CAAAtJ,SACND,eAACmL,GAAc,CAAAlL,SAAA,CACbC,cAACsJ,GAAY,CAACnJ,UAAU,gBAAeJ,SAAC,gBAExCC,cAACsN,GAAU,CAACC,QAAS,EAAExN,SAlGZ,CACjB,CACEH,KAAM,kBACNuI,YAAa,qFAEf,CACEvI,KAAM,gBACNuI,YAAa,0GAEf,CACEvI,KAAM,WACNuI,YAAa,uFAEf,CACEvI,KAAM,eACNuI,YAAa,+EAEf,CACEvI,KAAM,kBACNuI,YAAa,sFAEf,CACEvI,KAAM,oBACNuI,YAAa,0EA4EK5H,KAAI,CAACgM,EAAOtJ,IACtBjD,cAACzB,IAAOF,IAAG,CAETgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,GAAKwI,MAAe,GAARjH,GAAclD,SAElDD,eAACkO,GAAa,CAAAjO,SAAA,CACZC,cAACiO,GAAc,CAAAlO,SAAEwM,EAAM3M,OACvBI,cAACkO,GAAoB,CAAAnO,SAAEwM,EAAMpE,kBAP1BlF,YAefjD,cAACqJ,GAAO,CAAAtJ,SACND,eAACmL,GAAc,CAAAlL,SAAA,CACbC,cAACsJ,GAAY,CAACnJ,UAAU,gBAAeJ,SAAC,wBAExCD,eAACqO,GAAe,CAAApO,SAAA,CACdC,cAACzB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAG+C,GAAI,IAC3B9C,QAAS,CAAED,QAAS,EAAG+C,EAAG,GAC1B5C,WAAY,CAAEC,SAAU,IAAM3B,SAE9BD,eAACsO,GAAY,CAAArO,SAAA,CACXC,cAACqO,GAAa,CAAAtO,SAAC,uBACfC,cAACsO,GAAY,CAAAvO,SACV0O,EAAS/K,QAAQnD,KAAI,CAACsO,EAAM5L,IAC3BnD,eAACyO,GAAY,CAAAxO,SAAA,CACXD,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOG,OAAO,uBAAuBC,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAOrB,SAAA,CACpJC,cAAA,QAAMgB,EAAE,uCACRhB,cAAA,YAAU8O,OAAO,6BAEnB9O,cAAA,QAAAD,SAAO8O,MALU5L,YAY3BjD,cAACzB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAG+C,EAAG,IAC1B9C,QAAS,CAAED,QAAS,EAAG+C,EAAG,GAC1B5C,WAAY,CAAEC,SAAU,GAAKwI,MAAO,IAAMnK,SAE1CD,eAACsO,GAAY,CAAArO,SAAA,CACXC,cAACqO,GAAa,CAAAtO,SAAC,qBACfC,cAACsO,GAAY,CAAAvO,SACV0O,EAASC,OAAOnO,KAAI,CAACsO,EAAM5L,IAC1BnD,eAACyO,GAAY,CAAAxO,SAAA,CACXD,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOG,OAAO,sBAAsBC,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAOrB,SAAA,CACnJC,cAAA,QAAM+O,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,OAChClP,cAAA,QAAM+O,GAAG,IAAIC,GAAG,KAAKC,GAAG,KAAKC,GAAG,UAElClP,cAAA,QAAAD,SAAO8O,MALU5L,sBAcjB,ECxYtB,MAAMkM,GAAmBnR,IAAOK,GAAG;;;EAK7ByM,GAAa9M,IAAOK,GAAG;;;EAKvB0M,GAAY/M,IAAO+K,EAAE;;;;EAMrBS,GAAUxL,IAAOK,GAAG;;;;;EAOpB2M,GAAkBhN,IAAOiE,CAAC;;;;;EAO1BmN,GAAiBpR,IAAO2K,OAAO;;EAI/B0G,GAAiBrR,IAAOK,GAAG;;;;;;;;;;;EAa3BiR,GAActR,IAAOK,GAAG;;;;;;;EASxBkR,GAAcvR,IAAOK,GAAG;;EAIxBgN,GAAWrN,IAAOK,GAAG;;;;;;;EASrBiN,GAAYtN,IAAOiL,EAAE;;;;;EAOrBuG,GAAWxR,IAAOK,GAAG;;;;;;;EASrBoR,GAAczR,IAAOK,GAAG;;;;;;;;;;EAYxBkN,GAAcvN,IAAOK,GAAG,GAExBqR,GAAY1R,IAAO6H,EAAE;;;;;EAOrB8J,GAAW3R,IAAOiE,CAAC;;EAInB2N,GAAW5R,IAAOW,CAAC;;;;;;;EASnBuD,GAAclE,IAAOK,GAAG;;;;EAMxB8D,GAAanE,YAAOO,IAAOI,EAAE;;;;;;;;;;;;;;;EAiB7BkR,GAAW7R,IAAOK,GAAG;;;;;;EAQrByR,GAAY9R,IAAOiL,EAAE;;;;;EAOrB8G,GAAO/R,IAAOgS,IAAI,GAElBC,GAAUjS,IAAOK,GAAG;;;;;;;;;EAWpB6R,GAAYlS,IAAOK,GAAG;;EAItB8R,GAAYnS,IAAOoS,KAAK;;;;;EAOxBC,GAAYrS,IAAOsS,KAAK;;;;;;;;;;;;;;;;;;;EAqBxBC,GAAevS,IAAOwS,QAAQ;;;;;;;;;;;;;;;;;;;;;EAuB9BC,GAAezS,YAAOO,IAAOM,OAAO;;;;;;;;;;;;;;;;;;;;;;EAwBpC6R,GAAiB1S,YAAOO,IAAOF,IAAI;;;;;;;EAwU1BsS,OA/TCA,KACd,MAAOC,EAAUC,GAAe1R,mBAAS,CACvCS,KAAM,GACNkR,MAAO,GACPC,QAAS,GACTC,QAAS,MAEJC,EAAcC,GAAmB/R,oBAAS,IAC1CgS,EAAeC,GAAoBjS,oBAAS,GAE7CkS,EAAgBjK,IACpB,MAAM,KAAExH,EAAI,MAAE0R,GAAUlK,EAAEjE,OAC1B0N,GAAYU,IAAI,IAAUA,EAAM,CAAC3R,GAAO0R,KAAS,EA0B7C1O,EAAc,CAClB,CACEhD,KAAM,SACNiD,IAAK,mCACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAM+C,SAAS,UAAU/B,EAAE,mtBAAmtBgC,SAAS,eAI7vB,CACEpD,KAAM,WACNiD,IAAK,4DACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAMgB,EAAE,0fAId,CACEpB,KAAM,UACNiD,IAAK,0BACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAMgB,EAAE,+aAId,CACEpB,KAAM,WACNiD,IAAK,0DACLC,KACE9C,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,eAAeD,QAAQ,YAAWd,SACjEC,cAAA,QAAM+C,SAAS,UAAU/B,EAAE,yQAAyQgC,SAAS,gBAMrT,OACElD,eAACqP,GAAgB,CAAApP,SAAA,CACfC,cAACuD,EAAkB,IAEnBvD,cAAC8K,GAAU,CAAA/K,SACTD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAGb,EAAG,IAC1Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAC1BgB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAAC+K,GAAS,CAAAhL,SAAC,iBACXC,cAACwJ,GAAO,IACRxJ,cAACgL,GAAe,CAAAjL,SAAC,2EAMrBC,cAACoP,GAAc,CAAArP,SACbD,eAACuP,GAAc,CAAAtP,SAAA,CACbC,cAACsP,GAAW,CAAAvP,SACVD,eAACvB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAG+C,GAAI,IAC3B9C,QAAS,CAAED,QAAS,EAAG+C,EAAG,GAC1B5C,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BC,cAACqL,GAAQ,CAAAtL,SACPC,cAAA,OAAKG,UAAU,iBAAiB+G,MAAO,CACrCiD,aAAc,MACdC,SAAU,SACV+B,aAAc,SACdC,UAAW,gCACXzL,MAAO,QACPC,OAAQ,QACRyL,OAAQ,iBACRtM,SACAC,cAAA,OACEyK,IAA8B,wCAC9BC,IAAI,2BACJvK,UAAU,cACV+G,MAAO,CACLvG,MAAO,OACPC,OAAQ,OACR+J,UAAW,QACXC,eAAgB,SAChBT,aAAc,aAMtBrK,eAACuL,GAAQ,CAAAtL,SAAA,CACPC,cAACsL,GAAS,CAAAvL,SAAC,wBAEXD,eAAC0P,GAAQ,CAAAzP,SAAA,CACPC,cAACyP,GAAW,CAAA1P,SACVD,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,uBAAuBJ,QAAQ,YAAYK,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAOrB,SAAA,CACpJC,cAAA,QAAMgB,EAAE,gFACRhB,cAAA,YAAU8O,OAAO,wBAGrBhP,eAACyL,GAAW,CAAAxL,SAAA,CACVC,cAAC0P,GAAS,CAAA3P,SAAC,UACXC,cAAC4P,GAAQ,CAAC1M,KAAK,iDAAgDnD,SAAC,kDAMpED,eAAC0P,GAAQ,CAAAzP,SAAA,CACPC,cAACyP,GAAW,CAAA1P,SACVD,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,uBAAuBJ,QAAQ,YAAYK,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAOrB,SAAA,CACpJC,cAAA,QAAMgB,EAAE,mDACRhB,cAAA,UAAQwR,GAAG,KAAKC,GAAG,KAAKC,EAAE,WAG9B5R,eAACyL,GAAW,CAAAxL,SAAA,CACVC,cAAC0P,GAAS,CAAA3P,SAAC,aACXC,cAAC2P,GAAQ,CAAA5P,SAAC,qCAIdC,cAACsL,GAAS,CAACpE,MAAO,CAAEyK,UAAW,QAAS5R,SAAC,oBAEzCC,cAACkC,GAAW,CAAAnC,SACT6C,EAAYrC,KAAI,CAACC,EAAMyC,IACtBjD,cAACmC,GAAU,CAETe,KAAM1C,EAAKqC,IACXM,OAAO,SACPC,IAAI,sBACJ,aAAY5C,EAAKZ,KACjBQ,WAAY,CAAEK,GAAI,GAClBH,SAAU,CAAEG,EAAG,GAAIV,SAElBS,EAAKsC,MARDG,UAcbnD,eAACuL,GAAQ,CAAAtL,SAAA,CACPC,cAACsL,GAAS,CAAAvL,SAAC,WACXC,cAAC2P,GAAQ,CAACzI,MAAO,CAAEiF,aAAc,UAAWpM,SAAC,8EAI7CD,eAAA,OAAKoH,MAAO,CAAEmD,QAAS,OAAQuH,IAAK,OAAQC,SAAU,QAAS9R,SAAA,CAC7DD,eAACvB,IAAOI,EAAC,CACPuE,KAA+B,2BAC/BC,OAAO,SACP/C,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnB6G,MAAO,CAAEmD,QAAS,cAAeC,WAAY,SAAU7F,MAAO,wBAAyB1E,SAAA,CAEvFD,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,eAAeJ,QAAQ,YAAYK,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAQ8F,MAAO,CAAE4K,YAAa,UAAW/R,SAAA,CAC9KC,cAAA,QAAMgB,EAAE,8CACRhB,cAAA,YAAU8O,OAAO,qBACjB9O,cAAA,QAAM+O,GAAG,KAAKC,GAAG,KAAKC,GAAG,KAAKC,GAAG,SAC7B,yBAIRpP,eAACvB,IAAOI,EAAC,CACPuE,KAA+B,uBAC/BC,OAAO,SACP/C,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnB6G,MAAO,CAAEmD,QAAS,cAAeC,WAAY,SAAU7F,MAAO,wBAAyB1E,SAAA,CAEvFD,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,eAAeJ,QAAQ,YAAYK,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAQ8F,MAAO,CAAE4K,YAAa,UAAW/R,SAAA,CAC9KC,cAAA,QAAMgB,EAAE,8CACRhB,cAAA,YAAU8O,OAAO,qBACjB9O,cAAA,QAAM+O,GAAG,KAAKC,GAAG,KAAKC,GAAG,KAAKC,GAAG,SAC7B,8BAQhBlP,cAACuP,GAAW,CAAAxP,SACVC,cAACzB,IAAOF,IAAG,CACTgD,QAAS,CAAEC,QAAS,EAAG+C,EAAG,IAC1B9C,QAAS,CAAED,QAAS,EAAG+C,EAAG,GAC1B5C,WAAY,CAAEC,SAAU,GAAKwI,MAAO,IAAMnK,SAE1CD,eAAC+P,GAAQ,CAAA9P,SAAA,CACPC,cAAC8P,GAAS,CAAA/P,SAAC,sBAEVoR,GACCnR,cAAC0Q,GAAc,CACbrP,QAAS,CAAEC,QAAS,EAAGb,GAAI,IAC3Bc,QAAS,CAAED,QAAS,EAAGb,EAAG,GAAIV,SAC/B,wEAKHD,eAACiQ,GAAI,CAACgC,SA9NE3K,IACpBA,EAAE4K,iBACFd,GAAgB,GAGhBe,YAAW,KACTf,GAAgB,GAChBE,GAAiB,GACjBP,EAAY,CACVjR,KAAM,GACNkR,MAAO,GACPC,QAAS,GACTC,QAAS,KAIXiB,YAAW,KACTb,GAAiB,EAAM,GACtB,IAAK,GACP,KAAK,EA2MiCrR,SAAA,CAC3BD,eAACmQ,GAAO,CAAAlQ,SAAA,CACND,eAACoQ,GAAS,CAAAnQ,SAAA,CACRC,cAACmQ,GAAS,CAAC+B,QAAQ,OAAMnS,SAAC,cAC1BC,cAACqQ,GAAS,CACR8B,KAAK,OACLnI,GAAG,OACHpK,KAAK,OACL0R,MAAOV,EAAShR,KAChBwS,SAAUf,EACVgB,YAAY,WACZC,UAAQ,OAIZxS,eAACoQ,GAAS,CAAAnQ,SAAA,CACRC,cAACmQ,GAAS,CAAC+B,QAAQ,QAAOnS,SAAC,eAC3BC,cAACqQ,GAAS,CACR8B,KAAK,QACLnI,GAAG,QACHpK,KAAK,QACL0R,MAAOV,EAASE,MAChBsB,SAAUf,EACVgB,YAAY,mBACZC,UAAQ,UAKdxS,eAACoQ,GAAS,CAAAnQ,SAAA,CACRC,cAACmQ,GAAS,CAAC+B,QAAQ,UAASnS,SAAC,YAC7BC,cAACqQ,GAAS,CACR8B,KAAK,OACLnI,GAAG,UACHpK,KAAK,UACL0R,MAAOV,EAASG,QAChBqB,SAAUf,EACVgB,YAAY,kBACZC,UAAQ,OAIZxS,eAACoQ,GAAS,CAAAnQ,SAAA,CACRC,cAACmQ,GAAS,CAAC+B,QAAQ,UAASnS,SAAC,YAC7BC,cAACuQ,GAAY,CACXvG,GAAG,UACHpK,KAAK,UACL0R,MAAOV,EAASI,QAChBoB,SAAUf,EACVgB,YAAY,uBACZC,UAAQ,OAIZtS,cAACyQ,GAAY,CACX0B,KAAK,SACLI,SAAUtB,EACV7Q,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAON,SAEzBkR,EACCnR,eAAA0S,WAAA,CAAAzS,SAAA,CACED,eAAA,OAAKa,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOG,OAAO,eAAeC,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAQjB,UAAU,eAAcJ,SAAA,CACrKC,cAAA,UAAQwR,GAAG,KAAKC,GAAG,KAAKC,EAAE,KAAKpQ,QAAQ,SACvCtB,cAAA,QAAMgB,EAAE,+BACJ,gBAIR,kCASC,EC9jBvB,MAAMyR,GAAgBzU,YAAOO,IAAOM,OAAO;;;;;;;;;;;;;;;;EAkBrC6T,GAAa1U,YAAOO,IAAOF,IAAI;;;;;;;;;;;;;;EAgB/BsU,GAAa3U,IAAOK,GAAG;;;;EAMvBuU,GAAY5U,IAAO6H,EAAE;;;;EAMrBgN,GAAe7U,IAAOiE,CAAC;;;;EAMvB6Q,GAAe9U,IAAOK,GAAG;;;;;;;EASzB0U,GAAgB/U,IAAOK,GAAG;;;;;;;IAO5BH,GAA0B,QAAjBA,EAAM8U,OAAmB,4HAKhC;EAQAC,GAAWjV,IAAOgS,IAAI;;;;EAMtBkD,GAAYlV,IAAOsS,KAAK;;;;;;;;;;;;;;;;EAkBxB6C,GAAanV,IAAOa,MAAM;;;;;;;;;;EAmIjBuU,OAvHGA,KAChB,MAAOnU,EAAQC,GAAaC,oBAAS,IAC9BkU,EAAUC,GAAenU,mBAAS,CACvC,CACE6K,GAAI,EACJuJ,KAAM,qGACNP,OAAQ,UAGL1C,EAAOkD,GAAYrU,mBAAS,IAC7BsU,EAAiBhQ,iBAAO,MAO9BpE,qBAAU,KAJaqU,MAAO,IAADC,EACL,QAAtBA,EAAAF,EAAe/P,eAAO,IAAAiQ,GAAtBA,EAAwBC,eAAe,CAAEC,SAAU,UAAW,EAI9DH,EAAgB,GACf,CAACL,EAAUpU,IAGd,MAAM6U,EAAe9C,IACnB,MAAM+C,EAAe/C,EAAQgD,cAE7B,OAAID,EAAaE,SAAS,YAAcF,EAAaE,SAAS,QACrD,+OACEF,EAAaE,SAAS,UAAYF,EAAaE,SAAS,cAC1D,2LACEF,EAAaE,SAAS,YAAcF,EAAaE,SAAS,SAAWF,EAAaE,SAAS,SAC7F,kJACEF,EAAaE,SAAS,cAAgBF,EAAaE,SAAS,cAC9D,oNACEF,EAAaE,SAAS,UAAYF,EAAaE,SAAS,OAASF,EAAaE,SAAS,OACzF,4GAEA,oHACT,EAmBF,OACEnU,eAAA0S,WAAA,CAAAzS,SAAA,CACEC,cAACyS,GAAa,CACZrS,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IACnBK,QAASA,IAAMxB,GAAWD,GAAQc,SAEjCd,EACCe,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,eAAeJ,QAAQ,YAAWd,SAC/EC,cAAA,QAAMmB,cAAc,QAAQC,eAAe,QAAQF,YAAY,IAAIF,EAAE,2BAGvEhB,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,eAAeJ,QAAQ,YAAWd,SAC/EC,cAAA,QAAMmB,cAAc,QAAQC,eAAe,QAAQF,YAAY,IAAIF,EAAE,kHAK3EhB,cAACkU,IAAe,CAAAnU,SACbd,GACCa,eAAC4S,GAAU,CACTrR,QAAS,CAAEC,QAAS,EAAGb,EAAG,GAAIJ,MAAO,IACrCkB,QAAS,CAAED,QAAS,EAAGb,EAAG,EAAGJ,MAAO,GACpCmB,KAAM,CAAEF,QAAS,EAAGb,EAAG,GAAIJ,MAAO,IAClCoB,WAAY,CAAEC,SAAU,IAAM3B,SAAA,CAE9BD,eAAC6S,GAAU,CAAA5S,SAAA,CACTC,cAAC4S,GAAS,CAAA7S,SAAC,wBACXC,cAAC6S,GAAY,CAAA9S,SAAC,mCAGhBD,eAACgT,GAAY,CAAA/S,SAAA,CACVsT,EAAS9S,KAAKyQ,GACbhR,cAAC+S,GAAa,CAEZC,OAAQhC,EAAQgC,OAAOjT,SAEtBiR,EAAQuC,MAHJvC,EAAQhH,MAMjBhK,cAAA,OAAKqF,IAAKoO,OAGZ3T,eAACmT,GAAQ,CAAClB,SA3DE3K,IAEpB,GADAA,EAAE4K,kBACG1B,EAAM6D,OAAQ,OAGnB,MAAMC,EAAc,CAAEpK,GAAIqJ,EAAShG,OAAS,EAAGkG,KAAMjD,EAAO0C,OAAQ,QACpEM,EAAY,IAAID,EAAUe,IAC1BZ,EAAS,IAGTvB,YAAW,KACT,MAAMoC,EAAc,CAAErK,GAAIqJ,EAAShG,OAAS,EAAGkG,KAAMO,EAAYxD,GAAQ0C,OAAQ,OACjFM,GAAYgB,GAAgB,IAAIA,EAAcD,IAAa,GAC1D,IAAI,EA8CkCtU,SAAA,CAC/BC,cAACkT,GAAS,CACRf,KAAK,OACLb,MAAOhB,EACP8B,SAAWhL,GAAMoM,EAASpM,EAAEjE,OAAOmO,OACnCe,YAAY,sBAEdrS,cAACmT,GAAU,CAAChB,KAAK,SAAQpS,SACvBC,cAAA,OAAKW,MAAM,KAAKC,OAAO,KAAKE,KAAK,OAAOG,OAAO,eAAeJ,QAAQ,YAAWd,SAC/EC,cAAA,QAAMmB,cAAc,QAAQC,eAAe,QAAQF,YAAY,IAAIF,EAAE,mDAOhF,E,MCxMQuT,OAvBf,WACE,OACEvU,cAACwU,IAAM,CAAAzU,SACLD,eAAA,OAAKK,UAAU,MAAKJ,SAAA,CAClBC,cAAChB,EAAM,IACPgB,cAAA,QAAMG,UAAU,eAAcJ,SAC5BC,cAACkU,IAAe,CAACO,iBAAe,EAAA1U,SAC9BD,eAAC4U,IAAM,CAAA3U,SAAA,CACLC,cAAC2U,IAAK,CAACC,OAAK,EAAC/U,KAAK,IAAIgV,UAAW5K,KACjCjK,cAAC2U,IAAK,CAAC9U,KAAK,SAASgV,UAAW9I,KAChC/L,cAAC2U,IAAK,CAAC9U,KAAK,YAAYgV,UAAW5H,KACnCjN,cAAC2U,IAAK,CAAC9U,KAAK,UAAUgV,UAAWrG,KACjCxO,cAAC2U,IAAK,CAAC9U,KAAK,WAAWgV,UAAWlE,YAIxC3Q,cAACoT,GAAS,IACVpT,cAACwC,EAAM,QAIf,EC7BAsS,IAASC,OACP/U,cAACgV,IAAMC,WAAU,CAAAlV,SACfC,cAACuU,GAAG,MAENW,SAASC,eAAe,Q", "file": "static/js/main.6b78d22c.chunk.js", "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\n\nconst NavbarContainer = styled.header`\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n  transition: all 0.3s ease;\n  padding: ${props => props.scrolled ? '0.5rem 0' : '1rem 0'};\n  background: ${props => props.scrolled ? 'rgba(31, 34, 46, 0.8)' : 'transparent'};\n  backdrop-filter: ${props => props.scrolled ? 'blur(10px)' : 'none'};\n  box-shadow: ${props => props.scrolled ? '0 4px 30px rgba(0, 0, 0, 0.1)' : 'none'};\n`;\n\nconst NavContent = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n`;\n\nconst Logo = styled(motion.div)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  cursor: pointer;\n`;\n\nconst NavLinks = styled.nav`\n  display: none;\n  \n  @media (min-width: 768px) {\n    display: flex;\n    gap: 2rem;\n  }\n`;\n\nconst NavLink = styled(motion.a)`\n  color: var(--light-color);\n  font-weight: 500;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: var(--primary-color);\n  }\n`;\n\nconst MobileMenuButton = styled.button`\n  background: none;\n  border: none;\n  color: var(--light-color);\n  font-size: 1.5rem;\n  cursor: pointer;\n  display: block;\n  \n  @media (min-width: 768px) {\n    display: none;\n  }\n`;\n\nconst MobileMenu = styled(motion.div)`\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  background: rgba(31, 34, 46, 0.95);\n  backdrop-filter: blur(10px);\n  padding: 1rem 0;\n  \n  @media (min-width: 768px) {\n    display: none;\n  }\n`;\n\nconst MobileNavLink = styled.a`\n  display: block;\n  padding: 0.75rem 2rem;\n  color: var(--light-color);\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: var(--primary-color);\n  }\n`;\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  \n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n    \n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  \n  const navLinks = [\n    { name: 'Home', path: '/' },\n    { name: 'About', path: '/about' },\n    { name: 'Projects', path: '/projects' },\n    { name: 'Skills', path: '/skills' },\n    { name: 'Contact', path: '/contact' },\n  ];\n  \n  return (\n    <NavbarContainer scrolled={scrolled}>\n      <NavContent>\n        <Link to=\"/\">\n          <Logo \n            className=\"gradient-text\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Eric.dev\n          </Logo>\n        </Link>\n        \n        <NavLinks>\n          {navLinks.map((link) => (\n            <Link key={link.name} to={link.path}>\n              <NavLink \n                whileHover={{ y: -2 }}\n                whileTap={{ y: 0 }}\n              >\n                {link.name}\n              </NavLink>\n            </Link>\n          ))}\n        </NavLinks>\n        \n        <MobileMenuButton onClick={() => setIsOpen(!isOpen)}>\n          {isOpen ? (\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M6 18L18 6M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          ) : (\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M4 6H20M4 12H20M4 18H20\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          )}\n        </MobileMenuButton>\n      </NavContent>\n      \n      {isOpen && (\n        <MobileMenu\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.3 }}\n        >\n          {navLinks.map((link) => (\n            <Link key={link.name} to={link.path}>\n              <MobileNavLink onClick={() => setIsOpen(false)}>\n                {link.name}\n              </MobileNavLink>\n            </Link>\n          ))}\n        </MobileMenu>\n      )}\n    </NavbarContainer>\n  );\n};\n\nexport default Navbar;\n", "import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\n\nconst FooterContainer = styled.footer`\n  background: rgba(31, 34, 46, 0.8);\n  backdrop-filter: blur(10px);\n  padding: 3rem 0 1.5rem;\n  margin-top: 4rem;\n`;\n\nconst FooterContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n`;\n\nconst FooterTop = styled.div`\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  margin-bottom: 2rem;\n  \n  @media (min-width: 768px) {\n    flex-direction: row;\n    align-items: center;\n  }\n`;\n\nconst FooterLogo = styled.div`\n  margin-bottom: 1.5rem;\n  \n  @media (min-width: 768px) {\n    margin-bottom: 0;\n  }\n`;\n\nconst FooterTagline = styled.p`\n  color: #a0a0a0;\n  font-size: 0.9rem;\n  margin-top: 0.5rem;\n`;\n\nconst SocialLinks = styled.div`\n  display: flex;\n  gap: 1rem;\n`;\n\nconst SocialLink = styled(motion.a)`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: #2a2d3a;\n  border-radius: 50%;\n  color: #a0a0a0;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    color: var(--primary-color);\n  }\n`;\n\nconst FooterBottom = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: space-between;\n  padding-top: 1.5rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  \n  @media (min-width: 768px) {\n    flex-direction: row;\n  }\n`;\n\nconst Copyright = styled.p`\n  color: #a0a0a0;\n  font-size: 0.9rem;\n  margin-bottom: 1rem;\n  \n  @media (min-width: 768px) {\n    margin-bottom: 0;\n  }\n`;\n\nconst FooterLinks = styled.div`\n  display: flex;\n  gap: 1.5rem;\n`;\n\nconst FooterLink = styled.a`\n  color: #a0a0a0;\n  font-size: 0.9rem;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: var(--primary-color);\n  }\n`;\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  \n  const socialLinks = [\n    { \n      name: 'GitHub', \n      url: 'https://github.com/BhekumusaEric',\n      icon: (\n        <svg width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    { \n      name: 'LinkedIn', \n      url: 'https://www.linkedin.com/in/bhekumusaerickelvinntshwenya/',\n      icon: (\n        <svg width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n        </svg>\n      )\n    },\n    { \n      name: 'Twitter', \n      url: 'https://x.com/NoahEric_',\n      icon: (\n        <svg width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n        </svg>\n      )\n    },\n    { \n      name: 'Facebook', \n      url: 'https://www.facebook.com/profile.php?id=100085082100956',\n      icon: (\n        <svg width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    }\n  ];\n  \n  return (\n    <FooterContainer>\n      <FooterContent>\n        <FooterTop>\n          <FooterLogo>\n            <Link to=\"/\">\n              <h3 className=\"gradient-text\">Eric.dev</h3>\n            </Link>\n            <FooterTagline>AI/ML Developer & Problem Solver</FooterTagline>\n          </FooterLogo>\n          \n          <SocialLinks>\n            {socialLinks.map((link, index) => (\n              <SocialLink \n                key={index}\n                href={link.url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label={link.name}\n                whileHover={{ y: -3 }}\n                whileTap={{ y: 0 }}\n              >\n                {link.icon}\n              </SocialLink>\n            ))}\n          </SocialLinks>\n        </FooterTop>\n        \n        <FooterBottom>\n          <Copyright>\n            © {currentYear} Bhekumusa Eric Ntshwenya. All rights reserved.\n          </Copyright>\n          \n          <FooterLinks>\n            <FooterLink href=\"/privacy\">Privacy Policy</FooterLink>\n            <FooterLink href=\"/terms\">Terms of Service</FooterLink>\n          </FooterLinks>\n        </FooterBottom>\n      </FooterContent>\n    </FooterContainer>\n  );\n};\n\nexport default Footer;\n", "import React, { useRef, useEffect } from 'react';\nimport styled from 'styled-components';\n\nconst Canvas = styled.canvas`\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -1;\n`;\n\nconst StarfieldAnimation = () => {\n  const canvasRef = useRef(null);\n  \n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    let animationFrameId;\n    \n    // Set canvas dimensions\n    const setCanvasDimensions = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    \n    setCanvasDimensions();\n    window.addEventListener('resize', setCanvasDimensions);\n    \n    // Star properties\n    const stars = [];\n    const starCount = 200;\n    const maxDepth = 1000;\n    \n    // Initialize stars\n    for (let i = 0; i < starCount; i++) {\n      stars.push({\n        x: Math.random() * canvas.width - canvas.width / 2,\n        y: Math.random() * canvas.height - canvas.height / 2,\n        z: Math.random() * maxDepth,\n        color: `rgba(${Math.random() * 100 + 155}, ${Math.random() * 100 + 155}, ${Math.random() * 100 + 155}, ${Math.random() * 0.5 + 0.5})`\n      });\n    }\n    \n    // Animation loop\n    const animate = () => {\n      ctx.fillStyle = 'rgba(18, 18, 18, 0.2)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      \n      const centerX = canvas.width / 2;\n      const centerY = canvas.height / 2;\n      \n      // Update and draw stars\n      for (let i = 0; i < starCount; i++) {\n        const star = stars[i];\n        \n        // Move star closer to viewer\n        star.z -= 0.5;\n        \n        // Reset star if it's too close\n        if (star.z <= 0) {\n          star.x = Math.random() * canvas.width - centerX;\n          star.y = Math.random() * canvas.height - centerY;\n          star.z = maxDepth;\n        }\n        \n        // Calculate star position based on perspective\n        const scale = maxDepth / (maxDepth + star.z);\n        const x = centerX + star.x * scale;\n        const y = centerY + star.y * scale;\n        \n        // Calculate star size based on distance\n        const radius = scale * 1.5;\n        \n        // Draw star\n        ctx.beginPath();\n        ctx.arc(x, y, radius, 0, 2 * Math.PI);\n        ctx.fillStyle = star.color;\n        ctx.fill();\n      }\n      \n      animationFrameId = requestAnimationFrame(animate);\n    };\n    \n    animate();\n    \n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', setCanvasDimensions);\n      cancelAnimationFrame(animationFrameId);\n    };\n  }, []);\n  \n  return <Canvas ref={canvasRef} />;\n};\n\nexport default StarfieldAnimation;\n", "import React, { useState, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';\nimport styled from 'styled-components';\n\nconst Card = styled(motion.div)`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  overflow: hidden;\n  height: 100%;\n  transform-style: preserve-3d;\n  perspective: 1000px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.05);\n`;\n\nconst CardContent = styled.div`\n  padding: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n`;\n\nconst ProjectImage = styled.div`\n  height: 180px;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 1.5rem;\n  background: linear-gradient(135deg, #2a2d3a 0%, #1a1c24 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: transform 0.5s ease;\n  transform: ${props => props.hovered ? 'scale(1.05)' : 'scale(1)'};\n`;\n\nconst ProjectInitial = styled.div`\n  font-size: 3rem;\n  font-weight: 800;\n  color: var(--primary-color);\n`;\n\nconst ProjectPreview = styled.div`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin-top: 0.5rem;\n`;\n\nconst ProjectTitle = styled.h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.75rem;\n`;\n\nconst ProjectDescription = styled.p`\n  color: #a0a0a0;\n  margin-bottom: 1.5rem;\n  flex-grow: 1;\n  font-size: 0.95rem;\n`;\n\nconst TechStack = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1.5rem;\n`;\n\nconst TechTag = styled.span`\n  background: rgba(9, 105, 218, 0.1);\n  color: var(--primary-color);\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 500;\n`;\n\nconst CardLinks = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst CardLink = styled.a`\n  color: var(--primary-color);\n  font-size: 0.9rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    text-decoration: underline;\n  }\n`;\n\nconst ProjectCard = ({ project }) => {\n  const [hovered, setHovered] = useState(false);\n  const cardRef = useRef(null);\n  \n  // Mouse position for 3D effect\n  const x = useMotionValue(0);\n  const y = useMotionValue(0);\n  \n  // Smooth out the mouse movement\n  const springX = useSpring(x, { stiffness: 150, damping: 15 });\n  const springY = useSpring(y, { stiffness: 150, damping: 15 });\n  \n  // Transform mouse position into rotation values\n  const rotateX = useTransform(springY, [-0.5, 0.5], [10, -10]);\n  const rotateY = useTransform(springX, [-0.5, 0.5], [-10, 10]);\n  \n  // Handle mouse move for 3D effect\n  const handleMouseMove = (e) => {\n    if (!cardRef.current) return;\n    \n    const rect = cardRef.current.getBoundingClientRect();\n    const width = rect.width;\n    const height = rect.height;\n    \n    // Calculate normalized mouse position\n    const xValue = (e.clientX - rect.left) / width - 0.5;\n    const yValue = (e.clientY - rect.top) / height - 0.5;\n    \n    x.set(xValue);\n    y.set(yValue);\n  };\n  \n  // Reset card position when mouse leaves\n  const handleMouseLeave = () => {\n    x.set(0);\n    y.set(0);\n    setHovered(false);\n  };\n  \n  return (\n    <Card\n      ref={cardRef}\n      whileHover={{ scale: 1.02 }}\n      style={{\n        rotateX: rotateX,\n        rotateY: rotateY,\n      }}\n      onMouseMove={handleMouseMove}\n      onMouseEnter={() => setHovered(true)}\n      onMouseLeave={handleMouseLeave}\n    >\n      <CardContent>\n        <ProjectImage hovered={hovered}>\n          <div className=\"text-center\">\n            <ProjectInitial>{project.title.charAt(0)}</ProjectInitial>\n            <ProjectPreview>Project Preview</ProjectPreview>\n          </div>\n        </ProjectImage>\n        \n        <ProjectTitle className=\"gradient-text\">{project.title}</ProjectTitle>\n        <ProjectDescription>{project.description}</ProjectDescription>\n        \n        <TechStack>\n          {project.technologies.map((tech, index) => (\n            <TechTag key={index}>{tech}</TechTag>\n          ))}\n        </TechStack>\n        \n        <CardLinks>\n          {project.github && (\n            <CardLink \n              href={project.github}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <svg width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\n              </svg>\n              Code\n            </CardLink>\n          )}\n          \n          <Link to={`/projects/${project.slug}`}>\n            <CardLink as=\"span\">\n              View Details\n              <svg width=\"16\" height=\"16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\n              </svg>\n            </CardLink>\n          </Link>\n        </CardLinks>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ProjectCard;\n", "import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\nimport StarfieldAnimation from '../components/StarfieldAnimation';\nimport ProjectCard from '../components/ProjectCard';\n\nconst HomeContainer = styled.div`\n  position: relative;\n`;\n\nconst HeroSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  padding: 6rem 0;\n  position: relative;\n`;\n\nconst HeroContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  flex-direction: column;\n\n  @media (min-width: 992px) {\n    flex-direction: row;\n    align-items: center;\n  }\n`;\n\nconst HeroText = styled(motion.div)`\n  flex: 1;\n  margin-bottom: 3rem;\n\n  @media (min-width: 992px) {\n    margin-bottom: 0;\n    margin-right: 3rem;\n  }\n`;\n\nconst HeroTitle = styled.h1`\n  font-size: 2.5rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n\n  @media (min-width: 768px) {\n    font-size: 3.5rem;\n  }\n`;\n\nconst HeroSubtitle = styled.h2`\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #a0a0a0;\n  margin-bottom: 1.5rem;\n\n  @media (min-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n\nconst HeroDescription = styled.p`\n  font-size: 1.1rem;\n  color: #a0a0a0;\n  margin-bottom: 2rem;\n  max-width: 600px;\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n`;\n\nconst HeroVisual = styled(motion.div)`\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  height: 400px;\n`;\n\nconst Section = styled.section`\n  padding: 6rem 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 1rem;\n`;\n\nconst SectionSubtitle = styled.p`\n  font-size: 1.1rem;\n  color: #a0a0a0;\n  text-align: center;\n  max-width: 700px;\n  margin: 0 auto 3rem;\n`;\n\nconst Divider = styled.div`\n  width: 80px;\n  height: 4px;\n  background: var(--primary-color);\n  margin: 0 auto 2rem;\n`;\n\nconst ProjectsGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n\n  @media (min-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  @media (min-width: 1200px) {\n    grid-template-columns: repeat(3, 1fr);\n  }\n`;\n\nconst ViewAllButton = styled.div`\n  text-align: center;\n  margin-top: 3rem;\n`;\n\nconst CallToAction = styled.section`\n  padding: 6rem 0;\n  background: linear-gradient(rgba(9, 105, 218, 0.1), rgba(9, 105, 218, 0.05));\n  text-align: center;\n`;\n\nconst CTAContent = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 0 2rem;\n`;\n\nconst CTATitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n`;\n\nconst CTADescription = styled.p`\n  font-size: 1.2rem;\n  color: #a0a0a0;\n  margin-bottom: 2rem;\n`;\n\n// Featured projects data\nconst featuredProjects = [\n  {\n    id: 1,\n    title: 'SafeWayAI',\n    slug: 'safeway-ai',\n    description: 'AI-powered emergency detection platform that provides safe routes to destinations and detects danger in real-time.',\n    technologies: ['Python', 'Flet', 'Google Maps API', 'Firebase'],\n    github: 'https://github.com/BhekumusaEric/MSAIskillshackathon'\n  },\n  {\n    id: 2,\n    title: 'Smart Loan Approval Predictor',\n    slug: 'smart-loan-approval',\n    description: 'Mobile application for Eric Bank System that allows users to apply for loans and get instant approval predictions using ML.',\n    technologies: ['Python', 'Kivy', 'Machine Learning'],\n    github: 'https://github.com/BhekumusaEric/Smart-Loan-Approval-Predictor'\n  },\n  {\n    id: 3,\n    title: 'Eduwize',\n    slug: 'eduwize',\n    description: 'AI-powered learning platform designed to enhance student learning through personalized study material recommendations.',\n    technologies: ['Django', 'Azure Cognitive Services', 'Python'],\n    github: 'https://github.com/BhekumusaEric/Eduwize'\n  }\n];\n\nconst Home = () => {\n  return (\n    <HomeContainer>\n      <StarfieldAnimation />\n\n      <HeroSection>\n        <HeroContent>\n          <HeroText\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            <HeroTitle>\n              Hi, I'm <span className=\"gradient-text\">Eric Ntshwenya</span>\n            </HeroTitle>\n            <HeroSubtitle>\n              AI/ML Developer & Problem Solver\n            </HeroSubtitle>\n            <HeroDescription>\n              I build intelligent solutions that solve real-world problems. Specializing in AI, machine learning, and web development.\n            </HeroDescription>\n            <ButtonGroup>\n              <Link to=\"/projects\">\n                <motion.button\n                  className=\"btn btn-primary\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  View My Work\n                </motion.button>\n              </Link>\n              <Link to=\"/contact\">\n                <motion.button\n                  className=\"btn btn-outline\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Contact Me\n                </motion.button>\n              </Link>\n            </ButtonGroup>\n          </HeroText>\n\n          <HeroVisual\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <div className=\"glass-effect coin-container\" style={{\n              width: '100%',\n              height: '100%',\n              borderRadius: '50%',\n              overflow: 'hidden',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              position: 'relative'\n            }}>\n              <img\n                src={process.env.PUBLIC_URL + \"./images/54499080440_5b468ec2b6_o.jpg\"}\n                alt=\"Bhekumusa Eric Ntshwenya\"\n                className=\"coin-rotate\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover',\n                  objectPosition: 'center',\n                  borderRadius: '50%'\n                }}\n              />\n            </div>\n          </HeroVisual>\n        </HeroContent>\n      </HeroSection>\n\n      <Section>\n        <SectionTitle>Featured Projects</SectionTitle>\n        <Divider />\n        <SectionSubtitle>\n          Check out some of my recent work. These projects showcase my skills in AI, web development, and problem-solving.\n        </SectionSubtitle>\n\n        <ProjectsGrid>\n          {featuredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n            >\n              <ProjectCard project={project} />\n            </motion.div>\n          ))}\n        </ProjectsGrid>\n\n        <ViewAllButton>\n          <Link to=\"/projects\">\n            <motion.button\n              className=\"btn btn-outline\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              View All Projects\n            </motion.button>\n          </Link>\n        </ViewAllButton>\n      </Section>\n\n      <CallToAction>\n        <CTAContent>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            <CTATitle>Let's Work Together</CTATitle>\n            <CTADescription>\n              I'm currently available for freelance work and full-time positions in AI/ML development.\n            </CTADescription>\n            <Link to=\"/contact\">\n              <motion.button\n                className=\"btn btn-primary\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                Get In Touch\n              </motion.button>\n            </Link>\n          </motion.div>\n        </CTAContent>\n      </CallToAction>\n    </HomeContainer>\n  );\n};\n\nexport default Home;\n", "import React from 'react';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\nimport StarfieldAnimation from '../components/StarfieldAnimation';\n\nconst AboutContainer = styled.div`\n  position: relative;\n  padding-top: 80px;\n`;\n\nconst PageHeader = styled.div`\n  text-align: center;\n  padding: 4rem 0;\n`;\n\nconst PageTitle = styled.h1`\n  font-size: 3rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n`;\n\nconst Divider = styled.div`\n  width: 80px;\n  height: 4px;\n  background: var(--primary-color);\n  margin: 0 auto 1.5rem;\n`;\n\nconst PageDescription = styled.p`\n  font-size: 1.2rem;\n  color: #a0a0a0;\n  max-width: 700px;\n  margin: 0 auto;\n`;\n\nconst Section = styled.section`\n  padding: 5rem 0;\n\n  &:nth-child(even) {\n    background: rgba(31, 34, 46, 0.3);\n  }\n`;\n\nconst SectionContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  flex-direction: column;\n\n  @media (min-width: 992px) {\n    flex-direction: row;\n  }\n`;\n\nconst Column = styled.div`\n  flex: 1;\n  margin-bottom: 2rem;\n\n  @media (min-width: 992px) {\n    margin-bottom: 0;\n    ${props => props.right ? 'margin-left: 3rem;' : 'margin-right: 3rem;'}\n  }\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n`;\n\nconst Paragraph = styled.p`\n  color: #a0a0a0;\n  margin-bottom: 1.5rem;\n  line-height: 1.7;\n`;\n\nconst InfoCard = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n`;\n\nconst InfoTitle = styled.h3`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: var(--primary-color);\n  margin-bottom: 0.5rem;\n`;\n\nconst InfoContent = styled.p`\n  color: #a0a0a0;\n`;\n\nconst SkillsContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n\n  @media (min-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n`;\n\nconst SkillCategory = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n`;\n\nconst CategoryTitle = styled.h3`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: var(--primary-color);\n  margin-bottom: 1rem;\n`;\n\nconst SkillsList = styled.ul`\n  list-style: none;\n`;\n\nconst SkillItem = styled.li`\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.75rem;\n\n  &:before {\n    content: '';\n    display: inline-block;\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: var(--primary-color);\n    margin-right: 0.75rem;\n  }\n`;\n\nconst About = () => {\n  // Skills data\n  const skills = {\n    technical: [\n      'Python', 'JavaScript', 'React', 'Django', 'Flask',\n      'TensorFlow', 'Machine Learning', 'Data Analysis',\n      'Azure', 'Git', 'HTML/CSS'\n    ],\n    soft: [\n      'Problem Solving', 'Communication', 'Teamwork',\n      'Adaptability', 'Time Management', 'Critical Thinking'\n    ]\n  };\n\n  return (\n    <AboutContainer>\n      <StarfieldAnimation />\n\n      <PageHeader>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <PageTitle>About Me</PageTitle>\n          <Divider />\n          <PageDescription>\n            Get to know more about me, my background, and what drives me as a developer.\n          </PageDescription>\n        </motion.div>\n      </PageHeader>\n\n      <Section>\n        <SectionContent>\n          <Column>\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <SectionTitle className=\"gradient-text\">My Story</SectionTitle>\n              <Paragraph>\n                I'm Bhekumusa Eric Ntshwenya, a passionate software developer with a focus on AI and machine learning technologies. My journey in tech began with a curiosity about how software could solve real-world problems and make people's lives better.\n              </Paragraph>\n              <Paragraph>\n                Currently studying at WeThinkCode_, I'm constantly expanding my knowledge and skills in software development. I have a particular interest in artificial intelligence and its applications across different industries.\n              </Paragraph>\n              <Paragraph>\n                What drives me is the opportunity to create innovative solutions that have a meaningful impact. Whether it's developing an AI-powered safety app or building a machine learning model to predict loan approvals, I'm always looking for ways to leverage technology for good.\n              </Paragraph>\n              <Paragraph>\n                When I'm not coding, I enjoy learning about new technologies, contributing to open-source projects, and sharing my knowledge with others in the tech community.\n              </Paragraph>\n            </motion.div>\n          </Column>\n\n          <Column right>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <div className=\"glass-effect coin-container\" style={{\n                marginBottom: '2rem',\n                borderRadius: '50%',\n                overflow: 'hidden',\n                boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',\n                width: '250px',\n                height: '250px',\n                margin: '0 auto 2rem'\n              }}>\n                <img\n                  src={process.env.PUBLIC_URL + \"./images/54499080440_5b468ec2b6_o.jpg\"}\n                  alt=\"Bhekumusa Eric Ntshwenya\"\n                  className=\"coin-rotate\"\n                  style={{\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover',\n                    objectPosition: 'center',\n                    borderRadius: '50%'\n                  }}\n                />\n              </div>\n\n              <SectionTitle className=\"gradient-text\">Quick Facts</SectionTitle>\n\n              <InfoCard>\n                <InfoTitle>Location</InfoTitle>\n                <InfoContent>Johannesburg, South Africa</InfoContent>\n              </InfoCard>\n\n              <InfoCard>\n                <InfoTitle>Focus Areas</InfoTitle>\n                <InfoContent>AI/ML Development, Web Development, Mobile Apps</InfoContent>\n              </InfoCard>\n\n              <InfoCard>\n                <InfoTitle>Languages</InfoTitle>\n                <InfoContent>English (Fluent), Zulu (Native)</InfoContent>\n              </InfoCard>\n\n              <InfoCard>\n                <InfoTitle>Interests</InfoTitle>\n                <InfoContent>AI Research, Open Source, Continuous Learning</InfoContent>\n              </InfoCard>\n            </motion.div>\n          </Column>\n        </SectionContent>\n      </Section>\n\n      <Section>\n        <SectionContent>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            style={{ width: '100%' }}\n          >\n            <SectionTitle className=\"gradient-text\" style={{ textAlign: 'center', marginBottom: '2rem' }}>My Skills</SectionTitle>\n\n            <SkillsContainer>\n              <SkillCategory>\n                <CategoryTitle>Technical Skills</CategoryTitle>\n                <SkillsList>\n                  {skills.technical.map((skill, index) => (\n                    <SkillItem key={index}>{skill}</SkillItem>\n                  ))}\n                </SkillsList>\n              </SkillCategory>\n\n              <SkillCategory>\n                <CategoryTitle>Soft Skills</CategoryTitle>\n                <SkillsList>\n                  {skills.soft.map((skill, index) => (\n                    <SkillItem key={index}>{skill}</SkillItem>\n                  ))}\n                </SkillsList>\n              </SkillCategory>\n            </SkillsContainer>\n          </motion.div>\n        </SectionContent>\n      </Section>\n    </AboutContainer>\n  );\n};\n\nexport default About;\n", "import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\nimport StarfieldAnimation from '../components/StarfieldAnimation';\nimport ProjectCard from '../components/ProjectCard';\n\nconst ProjectsContainer = styled.div`\n  position: relative;\n  padding-top: 80px;\n`;\n\nconst PageHeader = styled.div`\n  text-align: center;\n  padding: 4rem 0;\n`;\n\nconst PageTitle = styled.h1`\n  font-size: 3rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n`;\n\nconst Divider = styled.div`\n  width: 80px;\n  height: 4px;\n  background: var(--primary-color);\n  margin: 0 auto 1.5rem;\n`;\n\nconst PageDescription = styled.p`\n  font-size: 1.2rem;\n  color: #a0a0a0;\n  max-width: 700px;\n  margin: 0 auto;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n  margin: 2rem 0 4rem;\n  padding: 0 2rem;\n`;\n\nconst FilterButton = styled.button`\n  padding: 0.5rem 1.5rem;\n  border-radius: 30px;\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: none;\n  \n  background-color: ${props => props.active ? 'var(--primary-color)' : 'rgba(31, 34, 46, 0.7)'};\n  color: ${props => props.active ? 'white' : '#a0a0a0'};\n  \n  &:hover {\n    background-color: ${props => props.active ? 'var(--primary-color)' : 'rgba(31, 34, 46, 0.9)'};\n  }\n`;\n\nconst ProjectsGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem 5rem;\n  \n  @media (min-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  @media (min-width: 1200px) {\n    grid-template-columns: repeat(3, 1fr);\n  }\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 5rem 0;\n  grid-column: 1 / -1;\n`;\n\nconst EmptyTitle = styled.h3`\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #a0a0a0;\n  margin-bottom: 1rem;\n`;\n\nconst EmptyDescription = styled.p`\n  color: #777;\n  max-width: 500px;\n  margin: 0 auto;\n`;\n\n// Projects data\nconst allProjects = [\n  {\n    id: 1,\n    title: 'SafeWayAI',\n    slug: 'safeway-ai',\n    description: 'AI-powered emergency detection platform that provides safe routes to destinations, detects danger in real-time, and allows users to report incidents. The app integrates with Google Maps and uses real crime data to analyze route safety.',\n    technologies: ['Python', 'Flet', 'Google Maps API', 'Firebase', 'Machine Learning'],\n    category: 'AI/ML',\n    github: 'https://github.com/BhekumusaEric/MSAIskillshackathon'\n  },\n  {\n    id: 2,\n    title: 'Smart Loan Approval Predictor',\n    slug: 'smart-loan-approval',\n    description: 'A mobile application for Eric Bank System that allows users to apply for loans and get instant approval predictions using machine learning. The app analyzes user data to determine loan eligibility.',\n    technologies: ['Python', 'Kivy', 'Machine Learning', 'SQLite'],\n    category: 'AI/ML',\n    github: 'https://github.com/BhekumusaEric/Smart-Loan-Approval-Predictor'\n  },\n  {\n    id: 3,\n    title: 'Eduwize',\n    slug: 'eduwize',\n    description: 'AI-powered learning platform designed to enhance student learning through personalized study material recommendations, quizzes with performance tracking, and resource management.',\n    technologies: ['Django', 'Azure Cognitive Services', 'Python', 'PostgreSQL'],\n    category: 'Web Development',\n    github: 'https://github.com/BhekumusaEric/Eduwize'\n  },\n  {\n    id: 4,\n    title: 'My Phone Repair Website',\n    slug: 'phone-repair-website',\n    description: 'A personal website for a mobile repair shop, showcasing services and allowing customers to book repairs online.',\n    technologies: ['HTML', 'CSS', 'JavaScript'],\n    category: 'Web Development',\n    github: 'https://github.com/BhekumusaEric/myphonerepairwebsite'\n  },\n  {\n    id: 5,\n    title: 'CharityBlockchain',\n    slug: 'charity-blockchain',\n    description: 'Ethereum blockchain project aimed at ensuring complete transparency in charity transactions. Integrates a solidity contract with a metamask wallet on Ganache interfaced accounts and deployment using Node.js.',\n    technologies: ['Blockchain', 'Solidity', 'Ethereum', 'Node.js'],\n    category: 'Blockchain',\n    github: 'https://github.com/BhekumusaEric/CharityBlockchain'\n  },\n  {\n    id: 6,\n    title: 'TruBudget',\n    slug: 'trubudget',\n    description: 'A blockchain-based workflow tool for efficient and transparent project management.',\n    technologies: ['Blockchain', 'JavaScript', 'React'],\n    category: 'Blockchain',\n    github: 'https://github.com/BhekumusaEric/TruBudget'\n  }\n];\n\nconst Projects = () => {\n  const [filter, setFilter] = useState('All');\n  const categories = ['All', 'AI/ML', 'Web Development', 'Blockchain'];\n  \n  // Filter projects based on selected category\n  const filteredProjects = filter === 'All' \n    ? allProjects \n    : allProjects.filter(project => project.category === filter);\n  \n  return (\n    <ProjectsContainer>\n      <StarfieldAnimation />\n      \n      <PageHeader>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <PageTitle>My Projects</PageTitle>\n          <Divider />\n          <PageDescription>\n            Explore my portfolio of projects spanning AI/ML, web development, and blockchain technologies.\n          </PageDescription>\n        </motion.div>\n      </PageHeader>\n      \n      <FilterContainer>\n        {categories.map((category, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: index * 0.1 }}\n          >\n            <FilterButton \n              active={filter === category}\n              onClick={() => setFilter(category)}\n            >\n              {category}\n            </FilterButton>\n          </motion.div>\n        ))}\n      </FilterContainer>\n      \n      <ProjectsGrid>\n        {filteredProjects.length > 0 ? (\n          filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n            >\n              <ProjectCard project={project} />\n            </motion.div>\n          ))\n        ) : (\n          <EmptyState>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <EmptyTitle>No projects found</EmptyTitle>\n              <EmptyDescription>\n                No projects match the selected filter. Try selecting a different category.\n              </EmptyDescription>\n            </motion.div>\n          </EmptyState>\n        )}\n      </ProjectsGrid>\n    </ProjectsContainer>\n  );\n};\n\nexport default Projects;\n", "import React from 'react';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\nimport StarfieldAnimation from '../components/StarfieldAnimation';\n\nconst SkillsContainer = styled.div`\n  position: relative;\n  padding-top: 80px;\n`;\n\nconst PageHeader = styled.div`\n  text-align: center;\n  padding: 4rem 0;\n`;\n\nconst PageTitle = styled.h1`\n  font-size: 3rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n`;\n\nconst Divider = styled.div`\n  width: 80px;\n  height: 4px;\n  background: var(--primary-color);\n  margin: 0 auto 1.5rem;\n`;\n\nconst PageDescription = styled.p`\n  font-size: 1.2rem;\n  color: #a0a0a0;\n  max-width: 700px;\n  margin: 0 auto;\n`;\n\nconst Section = styled.section`\n  padding: 5rem 0;\n  \n  &:nth-child(even) {\n    background: rgba(31, 34, 46, 0.3);\n  }\n`;\n\nconst SectionContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 3rem;\n`;\n\nconst SkillsGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 2rem;\n  \n  @media (min-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  @media (min-width: 1200px) {\n    grid-template-columns: repeat(${props => props.columns || 2}, 1fr);\n  }\n`;\n\nconst SkillCard = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n  height: 100%;\n`;\n\nconst SkillCategoryTitle = styled.h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: var(--primary-color);\n`;\n\nconst SkillItem = styled.div`\n  margin-bottom: 1.5rem;\n`;\n\nconst SkillHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n`;\n\nconst SkillName = styled.span`\n  font-weight: 500;\n`;\n\nconst SkillLevel = styled.span`\n  color: #a0a0a0;\n`;\n\nconst SkillBar = styled.div`\n  height: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  overflow: hidden;\n`;\n\nconst SkillProgress = styled.div`\n  height: 100%;\n  background: ${props => props.color || 'var(--primary-color)'};\n  border-radius: 4px;\n  width: ${props => props.level}%;\n  transition: width 1s ease-in-out;\n`;\n\nconst SoftSkillCard = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n  height: 100%;\n`;\n\nconst SoftSkillTitle = styled.h3`\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 0.75rem;\n  color: var(--primary-color);\n`;\n\nconst SoftSkillDescription = styled.p`\n  color: #a0a0a0;\n  font-size: 0.95rem;\n  line-height: 1.6;\n`;\n\nconst LearningSection = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 2rem;\n  \n  @media (min-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n`;\n\nconst LearningCard = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n`;\n\nconst LearningTitle = styled.h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst LearningList = styled.ul`\n  list-style: none;\n`;\n\nconst LearningItem = styled.li`\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n  color: #a0a0a0;\n  \n  svg {\n    margin-right: 0.75rem;\n    flex-shrink: 0;\n    margin-top: 0.25rem;\n  }\n`;\n\nconst Skills = () => {\n  // Technical skills data\n  const technicalSkills = [\n    {\n      category: 'Programming Languages',\n      skills: [\n        { name: 'Python', level: 95, color: '#3776AB' },\n        { name: 'JavaScript', level: 85, color: '#F7DF1E' },\n        { name: 'HTML', level: 90, color: '#E34F26' },\n        { name: 'CSS', level: 85, color: '#1572B6' },\n        { name: 'SQL', level: 80, color: '#4479A1' },\n      ]\n    },\n    {\n      category: 'AI & Machine Learning',\n      skills: [\n        { name: 'TensorFlow', level: 85, color: '#FF6F00' },\n        { name: 'Scikit-Learn', level: 90, color: '#F7931E' },\n        { name: 'Natural Language Processing', level: 80, color: '#8BC34A' },\n        { name: 'Computer Vision', level: 75, color: '#9C27B0' },\n        { name: 'Data Analysis', level: 85, color: '#03A9F4' },\n      ]\n    },\n    {\n      category: 'Web Development',\n      skills: [\n        { name: 'React', level: 80, color: '#61DAFB' },\n        { name: 'Django', level: 85, color: '#092E20' },\n        { name: 'Flask', level: 90, color: '#000000' },\n        { name: 'RESTful APIs', level: 85, color: '#FF5722' },\n        { name: 'Responsive Design', level: 80, color: '#9C27B0' },\n      ]\n    },\n    {\n      category: 'Tools & Technologies',\n      skills: [\n        { name: 'Git & GitHub', level: 90, color: '#F05032' },\n        { name: 'Azure', level: 75, color: '#0078D4' },\n        { name: 'Docker', level: 70, color: '#2496ED' },\n        { name: 'VS Code', level: 95, color: '#007ACC' },\n        { name: 'Linux', level: 80, color: '#FCC624' },\n      ]\n    }\n  ];\n  \n  // Soft skills data\n  const softSkills = [\n    { \n      name: 'Problem Solving', \n      description: 'Analytical approach to breaking down complex problems into manageable components.' \n    },\n    { \n      name: 'Communication', \n      description: 'Clear and effective communication of technical concepts to both technical and non-technical audiences.' \n    },\n    { \n      name: 'Teamwork', \n      description: 'Collaborative mindset with experience working in diverse teams on various projects.' \n    },\n    { \n      name: 'Adaptability', \n      description: 'Quick to learn new technologies and adapt to changing project requirements.' \n    },\n    { \n      name: 'Time Management', \n      description: 'Efficient prioritization of tasks to meet deadlines and deliver high-quality work.' \n    },\n    { \n      name: 'Critical Thinking', \n      description: 'Ability to evaluate information objectively and make sound decisions.' \n    },\n  ];\n  \n  // Learning data\n  const learning = {\n    current: [\n      'Advanced Deep Learning Techniques',\n      'Cloud Architecture on Azure',\n      'Blockchain Development',\n      'Next.js and Modern React Patterns'\n    ],\n    future: [\n      'Quantum Computing',\n      'Augmented Reality Development',\n      'Edge Computing and IoT',\n      'Advanced Natural Language Processing'\n    ]\n  };\n  \n  return (\n    <SkillsContainer>\n      <StarfieldAnimation />\n      \n      <PageHeader>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <PageTitle>My Skills</PageTitle>\n          <Divider />\n          <PageDescription>\n            A comprehensive overview of my technical expertise and professional capabilities.\n          </PageDescription>\n        </motion.div>\n      </PageHeader>\n      \n      <Section>\n        <SectionContent>\n          <SectionTitle className=\"gradient-text\">Technical Skills</SectionTitle>\n          \n          <SkillsGrid>\n            {technicalSkills.map((category, categoryIndex) => (\n              <motion.div\n                key={categoryIndex}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}\n              >\n                <SkillCard>\n                  <SkillCategoryTitle>{category.category}</SkillCategoryTitle>\n                  \n                  {category.skills.map((skill, skillIndex) => (\n                    <SkillItem key={skillIndex}>\n                      <SkillHeader>\n                        <SkillName>{skill.name}</SkillName>\n                        <SkillLevel>{skill.level}%</SkillLevel>\n                      </SkillHeader>\n                      <SkillBar>\n                        <SkillProgress level={skill.level} color={skill.color} />\n                      </SkillBar>\n                    </SkillItem>\n                  ))}\n                </SkillCard>\n              </motion.div>\n            ))}\n          </SkillsGrid>\n        </SectionContent>\n      </Section>\n      \n      <Section>\n        <SectionContent>\n          <SectionTitle className=\"gradient-text\">Soft Skills</SectionTitle>\n          \n          <SkillsGrid columns={3}>\n            {softSkills.map((skill, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                <SoftSkillCard>\n                  <SoftSkillTitle>{skill.name}</SoftSkillTitle>\n                  <SoftSkillDescription>{skill.description}</SoftSkillDescription>\n                </SoftSkillCard>\n              </motion.div>\n            ))}\n          </SkillsGrid>\n        </SectionContent>\n      </Section>\n      \n      <Section>\n        <SectionContent>\n          <SectionTitle className=\"gradient-text\">Continuous Learning</SectionTitle>\n          \n          <LearningSection>\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <LearningCard>\n                <LearningTitle>Currently Learning</LearningTitle>\n                <LearningList>\n                  {learning.current.map((item, index) => (\n                    <LearningItem key={index}>\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"var(--primary-color)\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                        <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n                        <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n                      </svg>\n                      <span>{item}</span>\n                    </LearningItem>\n                  ))}\n                </LearningList>\n              </LearningCard>\n            </motion.div>\n            \n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <LearningCard>\n                <LearningTitle>Future Interests</LearningTitle>\n                <LearningList>\n                  {learning.future.map((item, index) => (\n                    <LearningItem key={index}>\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"var(--accent-color)\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                        <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\n                        <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n                      </svg>\n                      <span>{item}</span>\n                    </LearningItem>\n                  ))}\n                </LearningList>\n              </LearningCard>\n            </motion.div>\n          </LearningSection>\n        </SectionContent>\n      </Section>\n    </SkillsContainer>\n  );\n};\n\nexport default Skills;\n", "import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport styled from 'styled-components';\nimport StarfieldAnimation from '../components/StarfieldAnimation';\n\nconst ContactContainer = styled.div`\n  position: relative;\n  padding-top: 80px;\n`;\n\nconst PageHeader = styled.div`\n  text-align: center;\n  padding: 4rem 0;\n`;\n\nconst PageTitle = styled.h1`\n  font-size: 3rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n`;\n\nconst Divider = styled.div`\n  width: 80px;\n  height: 4px;\n  background: var(--primary-color);\n  margin: 0 auto 1.5rem;\n`;\n\nconst PageDescription = styled.p`\n  font-size: 1.2rem;\n  color: #a0a0a0;\n  max-width: 700px;\n  margin: 0 auto;\n`;\n\nconst ContactSection = styled.section`\n  padding: 2rem 0 6rem;\n`;\n\nconst ContactContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  flex-direction: column;\n\n  @media (min-width: 992px) {\n    flex-direction: row;\n    gap: 3rem;\n  }\n`;\n\nconst ContactInfo = styled.div`\n  flex: 1;\n  margin-bottom: 3rem;\n\n  @media (min-width: 992px) {\n    margin-bottom: 0;\n  }\n`;\n\nconst ContactForm = styled.div`\n  flex: 2;\n`;\n\nconst InfoCard = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n`;\n\nconst InfoTitle = styled.h2`\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: var(--primary-color);\n`;\n\nconst InfoItem = styled.div`\n  display: flex;\n  margin-bottom: 1.5rem;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst IconWrapper = styled.div`\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  background: rgba(9, 105, 218, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 1rem;\n  flex-shrink: 0;\n`;\n\nconst InfoContent = styled.div``;\n\nconst InfoLabel = styled.h3`\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n  color: var(--primary-color);\n`;\n\nconst InfoText = styled.p`\n  color: #a0a0a0;\n`;\n\nconst InfoLink = styled.a`\n  color: #a0a0a0;\n  transition: color 0.3s ease;\n\n  &:hover {\n    color: var(--primary-color);\n  }\n`;\n\nconst SocialLinks = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 1.5rem;\n`;\n\nconst SocialLink = styled(motion.a)`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: rgba(31, 34, 46, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #a0a0a0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: var(--primary-color);\n    color: white;\n  }\n`;\n\nconst FormCard = styled.div`\n  background: rgba(31, 34, 46, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n`;\n\nconst FormTitle = styled.h2`\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: var(--primary-color);\n`;\n\nconst Form = styled.form``;\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n  margin-bottom: 1.5rem;\n\n  @media (min-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 1.5rem;\n`;\n\nconst FormLabel = styled.label`\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #a0a0a0;\n  font-size: 0.95rem;\n`;\n\nconst FormInput = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem;\n  background: rgba(31, 34, 46, 0.5);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  color: white;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-color);\n    box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.2);\n  }\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.3);\n  }\n`;\n\nconst FormTextarea = styled.textarea`\n  width: 100%;\n  padding: 0.75rem 1rem;\n  background: rgba(31, 34, 46, 0.5);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  color: white;\n  font-size: 1rem;\n  min-height: 150px;\n  resize: vertical;\n  transition: all 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-color);\n    box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.2);\n  }\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.3);\n  }\n`;\n\nconst SubmitButton = styled(motion.button)`\n  background: var(--primary-color);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  padding: 0.75rem 2rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n\n  &:disabled {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n\n  svg {\n    margin-right: 0.5rem;\n  }\n`;\n\nconst SuccessMessage = styled(motion.div)`\n  background: rgba(46, 204, 113, 0.1);\n  border: 1px solid rgba(46, 204, 113, 0.3);\n  color: #2ecc71;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1.5rem;\n`;\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setSubmitSuccess(true);\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n\n      // Reset success message after 5 seconds\n      setTimeout(() => {\n        setSubmitSuccess(false);\n      }, 5000);\n    }, 1500);\n  };\n\n  // Social media links\n  const socialLinks = [\n    {\n      name: 'GitHub',\n      url: 'https://github.com/BhekumusaEric',\n      icon: (\n        <svg width=\"18\" height=\"18\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      name: 'LinkedIn',\n      url: 'https://www.linkedin.com/in/bhekumusaerickelvinntshwenya/',\n      icon: (\n        <svg width=\"18\" height=\"18\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'Twitter',\n      url: 'https://x.com/NoahEric_',\n      icon: (\n        <svg width=\"18\" height=\"18\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n        </svg>\n      )\n    },\n    {\n      name: 'Facebook',\n      url: 'https://www.facebook.com/profile.php?id=100085082100956',\n      icon: (\n        <svg width=\"18\" height=\"18\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <ContactContainer>\n      <StarfieldAnimation />\n\n      <PageHeader>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <PageTitle>Get In Touch</PageTitle>\n          <Divider />\n          <PageDescription>\n            Have a question or want to work together? Feel free to reach out!\n          </PageDescription>\n        </motion.div>\n      </PageHeader>\n\n      <ContactSection>\n        <ContactContent>\n          <ContactInfo>\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <InfoCard>\n                <div className=\"coin-container\" style={{\n                  borderRadius: '50%',\n                  overflow: 'hidden',\n                  marginBottom: '1.5rem',\n                  boxShadow: '0 5px 15px rgba(0, 0, 0, 0.2)',\n                  width: '200px',\n                  height: '200px',\n                  margin: '0 auto 1.5rem'\n                }}>\n                  <img\n                    src={process.env.PUBLIC_URL + \"./images/54499080440_5b468ec2b6_o.jpg\"}\n                    alt=\"Bhekumusa Eric Ntshwenya\"\n                    className=\"coin-rotate\"\n                    style={{\n                      width: '100%',\n                      height: '100%',\n                      objectFit: 'cover',\n                      objectPosition: 'center',\n                      borderRadius: '50%'\n                    }}\n                  />\n                </div>\n              </InfoCard>\n\n              <InfoCard>\n                <InfoTitle>Contact Information</InfoTitle>\n\n                <InfoItem>\n                  <IconWrapper>\n                    <svg width=\"24\" height=\"24\" fill=\"none\" stroke=\"var(--primary-color)\" viewBox=\"0 0 24 24\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                      <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"></path>\n                      <polyline points=\"22,6 12,13 2,6\"></polyline>\n                    </svg>\n                  </IconWrapper>\n                  <InfoContent>\n                    <InfoLabel>Email</InfoLabel>\n                    <InfoLink href=\"mailto:<EMAIL>\">\n                      <EMAIL>\n                    </InfoLink>\n                  </InfoContent>\n                </InfoItem>\n\n                <InfoItem>\n                  <IconWrapper>\n                    <svg width=\"24\" height=\"24\" fill=\"none\" stroke=\"var(--primary-color)\" viewBox=\"0 0 24 24\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                      <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n                      <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n                    </svg>\n                  </IconWrapper>\n                  <InfoContent>\n                    <InfoLabel>Location</InfoLabel>\n                    <InfoText>Johannesburg, South Africa</InfoText>\n                  </InfoContent>\n                </InfoItem>\n\n                <InfoTitle style={{ marginTop: '2rem' }}>Connect With Me</InfoTitle>\n\n                <SocialLinks>\n                  {socialLinks.map((link, index) => (\n                    <SocialLink\n                      key={index}\n                      href={link.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      aria-label={link.name}\n                      whileHover={{ y: -5 }}\n                      whileTap={{ y: 0 }}\n                    >\n                      {link.icon}\n                    </SocialLink>\n                  ))}\n                </SocialLinks>\n              </InfoCard>\n\n              <InfoCard>\n                <InfoTitle>Resume</InfoTitle>\n                <InfoText style={{ marginBottom: '1.5rem' }}>\n                  View my professional resume to learn more about my experience and skills.\n                </InfoText>\n\n                <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n                  <motion.a\n                    href={process.env.PUBLIC_URL + \"./static/ats-resume.html\"}\n                    target=\"_blank\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    style={{ display: 'inline-flex', alignItems: 'center', color: 'var(--primary-color)' }}\n                  >\n                    <svg width=\"20\" height=\"20\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" style={{ marginRight: '0.5rem' }}>\n                      <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path>\n                      <polyline points=\"7 10 12 15 17 10\"></polyline>\n                      <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"></line>\n                    </svg>\n                    ATS-Friendly Resume\n                  </motion.a>\n\n                  <motion.a\n                    href={process.env.PUBLIC_URL + \"./static/resume.html\"}\n                    target=\"_blank\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    style={{ display: 'inline-flex', alignItems: 'center', color: 'var(--primary-color)' }}\n                  >\n                    <svg width=\"20\" height=\"20\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" style={{ marginRight: '0.5rem' }}>\n                      <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path>\n                      <polyline points=\"7 10 12 15 17 10\"></polyline>\n                      <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"></line>\n                    </svg>\n                    Visual Resume\n                  </motion.a>\n                </div>\n              </InfoCard>\n            </motion.div>\n          </ContactInfo>\n\n          <ContactForm>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <FormCard>\n                <FormTitle>Send Me a Message</FormTitle>\n\n                {submitSuccess && (\n                  <SuccessMessage\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                  >\n                    Your message has been sent successfully! I'll get back to you soon.\n                  </SuccessMessage>\n                )}\n\n                <Form onSubmit={handleSubmit}>\n                  <FormRow>\n                    <FormGroup>\n                      <FormLabel htmlFor=\"name\">Your Name</FormLabel>\n                      <FormInput\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleChange}\n                        placeholder=\"John Doe\"\n                        required\n                      />\n                    </FormGroup>\n\n                    <FormGroup>\n                      <FormLabel htmlFor=\"email\">Your Email</FormLabel>\n                      <FormInput\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleChange}\n                        placeholder=\"<EMAIL>\"\n                        required\n                      />\n                    </FormGroup>\n                  </FormRow>\n\n                  <FormGroup>\n                    <FormLabel htmlFor=\"subject\">Subject</FormLabel>\n                    <FormInput\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleChange}\n                      placeholder=\"Project Inquiry\"\n                      required\n                    />\n                  </FormGroup>\n\n                  <FormGroup>\n                    <FormLabel htmlFor=\"message\">Message</FormLabel>\n                    <FormTextarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleChange}\n                      placeholder=\"Your message here...\"\n                      required\n                    />\n                  </FormGroup>\n\n                  <SubmitButton\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"animate-spin\">\n                          <circle cx=\"12\" cy=\"12\" r=\"10\" opacity=\"0.25\" />\n                          <path d=\"M12 2a10 10 0 0 1 10 10\" />\n                        </svg>\n                        Sending...\n                      </>\n                    ) : (\n                      'Send Message'\n                    )}\n                  </SubmitButton>\n                </Form>\n              </FormCard>\n            </motion.div>\n          </ContactForm>\n        </ContactContent>\n      </ContactSection>\n    </ContactContainer>\n  );\n};\n\nexport default Contact;\n", "import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport styled from 'styled-components';\n\nconst ChatbotButton = styled(motion.button)`\n  position: fixed;\n  bottom: 2rem;\n  right: 2rem;\n  width: 3.5rem;\n  height: 3.5rem;\n  border-radius: 50%;\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 100;\n  box-shadow: 0 4px 20px rgba(9, 105, 218, 0.3);\n`;\n\nconst ChatWindow = styled(motion.div)`\n  position: fixed;\n  bottom: 6.5rem;\n  right: 2rem;\n  width: 320px;\n  height: 450px;\n  background: rgba(31, 34, 46, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  z-index: 100;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n`;\n\nconst ChatHeader = styled.div`\n  background-color: var(--primary-color);\n  padding: 1rem;\n  color: white;\n`;\n\nconst ChatTitle = styled.h3`\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 600;\n`;\n\nconst ChatSubtitle = styled.p`\n  margin: 0.25rem 0 0;\n  font-size: 0.8rem;\n  opacity: 0.8;\n`;\n\nconst ChatMessages = styled.div`\n  flex: 1;\n  padding: 1rem;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n\nconst MessageBubble = styled.div`\n  max-width: 80%;\n  padding: 0.75rem 1rem;\n  border-radius: 12px;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  \n  ${props => props.sender === 'bot' ? `\n    align-self: flex-start;\n    background-color: #2a2d3a;\n    border-bottom-left-radius: 4px;\n    color: white;\n  ` : `\n    align-self: flex-end;\n    background-color: var(--primary-color);\n    border-bottom-right-radius: 4px;\n    color: white;\n  `}\n`;\n\nconst ChatForm = styled.form`\n  display: flex;\n  padding: 0.75rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst ChatInput = styled.input`\n  flex: 1;\n  padding: 0.75rem 1rem;\n  border: none;\n  border-radius: 20px 0 0 20px;\n  background-color: #2a2d3a;\n  color: white;\n  font-size: 0.9rem;\n  \n  &:focus {\n    outline: none;\n  }\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n`;\n\nconst SendButton = styled.button`\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n  border-radius: 0 20px 20px 0;\n  padding: 0 1rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst AIChatbot = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState([\n    { \n      id: 1, \n      text: \"Hi there! I'm Eric's AI assistant. How can I help you learn more about Eric's skills and projects?\", \n      sender: 'bot' \n    }\n  ]);\n  const [input, setInput] = useState('');\n  const messagesEndRef = useRef(null);\n  \n  // Auto-scroll to bottom of messages\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n  \n  useEffect(() => {\n    scrollToBottom();\n  }, [messages, isOpen]);\n  \n  // Predefined responses based on keywords\n  const getResponse = (message) => {\n    const lowerMessage = message.toLowerCase();\n    \n    if (lowerMessage.includes('project') || lowerMessage.includes('work')) {\n      return \"Eric has worked on several impressive projects including SafeWayAI (an AI-powered emergency detection platform), Smart Loan Approval Predictor, Eduwize (an AI learning platform), and more. You can check them out in the Projects section!\";\n    } else if (lowerMessage.includes('skill') || lowerMessage.includes('technology')) {\n      return \"Eric is skilled in Python, JavaScript, React, TensorFlow, Django, Flask, Azure, and more. He specializes in AI/ML development and has experience with mobile app development using Kivy.\";\n    } else if (lowerMessage.includes('contact') || lowerMessage.includes('hire') || lowerMessage.includes('email')) {\n      return \"You can contact Eric through the Contact form on this website, or connect with him on LinkedIn at linkedin.com/in/bhekumusaerickelvinntshwenya/\";\n    } else if (lowerMessage.includes('education') || lowerMessage.includes('background')) {\n      return \"Eric is a software developer with expertise in AI, mobile applications, and web development. He's currently studying at WeThinkCode and continuously learning about AI, machine learning, and cloud technologies.\";\n    } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {\n      return \"Hello! I'm Eric's AI assistant. What would you like to know about Eric's skills, projects, or experience?\";\n    } else {\n      return \"I'm not sure I understand. Would you like to know about Eric's projects, skills, education, or how to contact him?\";\n    }\n  };\n  \n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (!input.trim()) return;\n    \n    // Add user message\n    const userMessage = { id: messages.length + 1, text: input, sender: 'user' };\n    setMessages([...messages, userMessage]);\n    setInput('');\n    \n    // Simulate AI response with a slight delay\n    setTimeout(() => {\n      const botResponse = { id: messages.length + 2, text: getResponse(input), sender: 'bot' };\n      setMessages(prevMessages => [...prevMessages, botResponse]);\n    }, 500);\n  };\n  \n  return (\n    <>\n      <ChatbotButton\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        onClick={() => setIsOpen(!isOpen)}\n      >\n        {isOpen ? (\n          <svg width=\"20\" height=\"20\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        ) : (\n          <svg width=\"20\" height=\"20\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z\" />\n          </svg>\n        )}\n      </ChatbotButton>\n      \n      <AnimatePresence>\n        {isOpen && (\n          <ChatWindow\n            initial={{ opacity: 0, y: 20, scale: 0.9 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: 20, scale: 0.9 }}\n            transition={{ duration: 0.2 }}\n          >\n            <ChatHeader>\n              <ChatTitle>Eric's AI Assistant</ChatTitle>\n              <ChatSubtitle>Ask me anything about Eric!</ChatSubtitle>\n            </ChatHeader>\n            \n            <ChatMessages>\n              {messages.map((message) => (\n                <MessageBubble\n                  key={message.id}\n                  sender={message.sender}\n                >\n                  {message.text}\n                </MessageBubble>\n              ))}\n              <div ref={messagesEndRef} />\n            </ChatMessages>\n            \n            <ChatForm onSubmit={handleSubmit}>\n              <ChatInput\n                type=\"text\"\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                placeholder=\"Type a message...\"\n              />\n              <SendButton type=\"submit\">\n                <svg width=\"16\" height=\"16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                </svg>\n              </SendButton>\n            </ChatForm>\n          </ChatWindow>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default AIChatbot;\n", "import React from 'react';\nimport { HashRouter as Router, Route, Switch } from 'react-router-dom';\nimport { AnimatePresence } from 'framer-motion';\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Projects from './pages/Projects';\nimport Skills from './pages/Skills';\nimport Contact from './pages/Contact';\nimport AIChatbot from './components/AIChatbot';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"app\">\n        <Navbar />\n        <main className=\"main-content\">\n          <AnimatePresence exitBeforeEnter>\n            <Switch>\n              <Route exact path=\"/\" component={Home} />\n              <Route path=\"/about\" component={About} />\n              <Route path=\"/projects\" component={Projects} />\n              <Route path=\"/skills\" component={Skills} />\n              <Route path=\"/contact\" component={Contact} />\n            </Switch>\n          </AnimatePresence>\n        </main>\n        <AIChatbot />\n        <Footer />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n", "import React from 'react';\nimport ReactDOM from 'react-dom';\nimport './index.css';\nimport App from './App';\n\nReactDOM.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>,\n  document.getElementById('root')\n);\n"], "sourceRoot": ""}