<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON>V</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: #0969DA;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }

        .header h2 {
            font-size: 18px;
            font-weight: 400;
            margin-bottom: 15px;
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            font-size: 14px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .main {
            padding: 30px;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #0969DA;
            border-bottom: 2px solid #0969DA;
            padding-bottom: 5px;
        }

        .profile-summary {
            margin-bottom: 20px;
        }

        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .skill {
            background-color: #e9f2fd;
            color: #0969DA;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
        }

        .experience-item, .education-item, .project-item {
            margin-bottom: 20px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .item-title {
            font-weight: 600;
            font-size: 16px;
        }

        .item-subtitle {
            font-weight: 500;
            color: #555;
            font-size: 15px;
        }

        .item-date {
            color: #777;
            font-size: 14px;
        }

        .item-description {
            font-size: 14px;
            margin-top: 5px;
        }

        .item-bullets {
            margin-left: 20px;
            margin-top: 5px;
        }

        .item-bullets li {
            font-size: 14px;
            margin-bottom: 3px;
        }

        .references {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .reference {
            flex: 1;
            min-width: 200px;
        }

        .reference-name {
            font-weight: 600;
        }

        .reference-title {
            font-style: italic;
            color: #555;
            font-size: 14px;
        }

        .reference-contact {
            font-size: 14px;
        }

        .print-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #0969DA;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        @media print {
            body {
                padding: 0;
                background-color: white;
            }

            .container {
                box-shadow: none;
            }

            .print-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BHEKUMUSA ERIC NTSHWENYA</h1>
            <h2>🏆 Hackathon Winner | Software Engineer | AI/ML Developer</h2>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📱</span>
                    <span>+27 67 137 2148</span>
                </div>
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>🔗</span>
                    <span>linkedin.com/in/bhekumusaerickelvinntshwenya</span>
                </div>
                <div class="contact-item">
                    <span>💻</span>
                    <span>github.com/BhekumusaEric</span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>Johannesburg, South Africa</span>
                </div>
            </div>
        </div>

        <div class="main">
            <div class="section profile-summary">
                <p><strong>🏆 Sappas Hackathon Winner</strong> and accomplished software engineer with expertise in full-stack development, AI/ML technologies, and enterprise programming. Proven track record of delivering innovative solutions using Python, JavaScript, Java, and C#. Currently pursuing advanced software engineering at WeThinkCode_ while maintaining active development in machine learning and modern web technologies. <strong>SAP Tech Consultant certified</strong> with hands-on experience in enterprise solutions, scalable application development, and competitive programming success.</p>
            </div>

            <div class="section">
                <h3 class="section-title">TECHNICAL SKILLS</h3>
                <div class="skills-container">
                    <span class="skill">Python</span>
                    <span class="skill">Java</span>
                    <span class="skill">C#</span>
                    <span class="skill">JavaScript</span>
                    <span class="skill">HTML/CSS</span>
                    <span class="skill">React</span>
                    <span class="skill">Django</span>
                    <span class="skill">Flask</span>
                    <span class="skill">Spring Boot</span>
                    <span class="skill">.NET Framework</span>
                    <span class="skill">SQL</span>
                    <span class="skill">SAP</span>
                    <span class="skill">Enterprise Solutions</span>
                    <span class="skill">Machine Learning</span>
                    <span class="skill">TensorFlow</span>
                    <span class="skill">Neural Networks</span>
                    <span class="skill">Git</span>
                    <span class="skill">RESTful APIs</span>
                    <span class="skill">Blockchain</span>
                    <span class="skill">Data Science</span>
                    <span class="skill">Business Process Optimization</span>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">🏆 ACHIEVEMENTS & RECOGNITION</h3>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">🏆 Sappas Hackathon Winner</div>
                        <div class="item-date">2024</div>
                    </div>
                    <div class="item-subtitle">Competitive Programming & Innovation</div>
                    <ul class="item-bullets">
                        <li>Emerged victorious in the prestigious Sappas hackathon competition among top developers</li>
                        <li>Demonstrated exceptional problem-solving skills and innovative thinking under pressure</li>
                        <li>Developed cutting-edge solution showcasing technical excellence and creativity</li>
                        <li>Recognized for outstanding performance in competitive programming environment</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">🎯 Technical Skill Expansion & SAP Certification</div>
                        <div class="item-date">2024</div>
                    </div>
                    <div class="item-subtitle">Enterprise Programming & Business Solutions</div>
                    <ul class="item-bullets">
                        <li>Successfully mastered Java programming for enterprise-level application development</li>
                        <li>Achieved proficiency in C# and .NET framework for Microsoft technology stack</li>
                        <li>Earned SAP Tech Consultant certification, demonstrating expertise in enterprise business solutions</li>
                        <li>Enhanced full-stack development capabilities across multiple technology stacks</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">PROJECTS</h3>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">SafeWayAI</div>
                    </div>
                    <div class="item-subtitle">AI-powered Emergency Detection Platform</div>
                    <ul class="item-bullets">
                        <li>Developed an AI-powered platform that provides safe routes to destinations and detects danger in real-time</li>
                        <li>Integrated with Google Maps API and Firebase for real-time data processing</li>
                        <li>Implemented machine learning algorithms to analyze route safety based on crime data</li>
                        <li>Technologies: Python, Flet, Google Maps API, Firebase, Machine Learning</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">Smart Loan Approval Predictor</div>
                    </div>
                    <div class="item-subtitle">Mobile Banking Application</div>
                    <ul class="item-bullets">
                        <li>Created a mobile application for loan applications with instant approval predictions using ML</li>
                        <li>Designed and implemented user-friendly interfaces for data input and result visualization</li>
                        <li>Built predictive models to analyze user data and determine loan eligibility</li>
                        <li>Technologies: Python, Kivy, Machine Learning, SQLite</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">Eduwize</div>
                    </div>
                    <div class="item-subtitle">AI-powered Learning Platform</div>
                    <ul class="item-bullets">
                        <li>Developed a learning platform that enhances student learning through personalized study material recommendations</li>
                        <li>Implemented performance tracking and resource management features</li>
                        <li>Integrated Azure Cognitive Services for content analysis and recommendation</li>
                        <li>Technologies: Django, Azure Cognitive Services, Python, PostgreSQL</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">🚀 My Portfolio Website (LIVE)</div>
                    </div>
                    <div class="item-subtitle">Professional Portfolio & Resume Platform</div>
                    <ul class="item-bullets">
                        <li>Developed and deployed a comprehensive portfolio website showcasing projects and achievements</li>
                        <li>Implemented responsive design with modern UI/UX principles and animations</li>
                        <li>Features dynamic project filtering, achievement badges, and professional timeline</li>
                        <li>Technologies: React, JavaScript, HTML/CSS, GitHub Pages</li>
                        <li>🌐 Live: https://bhekumusaeric.github.io/My-Portfolio</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">TrainJava</div>
                    </div>
                    <div class="item-subtitle">Java Training & Practice Repository</div>
                    <ul class="item-bullets">
                        <li>Comprehensive Java training repository demonstrating OOP concepts and data structures</li>
                        <li>Implemented various algorithms and design patterns in Java</li>
                        <li>Showcases enterprise-level programming practices and code organization</li>
                        <li>Technologies: Java, Object-Oriented Programming, Data Structures, Algorithms</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">NeuroStrike</div>
                    </div>
                    <div class="item-subtitle">Advanced Neural Network Implementation</div>
                    <ul class="item-bullets">
                        <li>Developed advanced neural network implementation for pattern recognition</li>
                        <li>Implemented deep learning algorithms for machine learning applications</li>
                        <li>Optimized model performance and accuracy through advanced techniques</li>
                        <li>Technologies: Python, Neural Networks, Deep Learning, AI</li>
                    </ul>
                </div>

                <div class="project-item">
                    <div class="item-header">
                        <div class="item-title">AWS Data Code</div>
                    </div>
                    <div class="item-subtitle">Cloud-based Data Processing Solution</div>
                    <ul class="item-bullets">
                        <li>Built scalable data processing and analytics solution on AWS infrastructure</li>
                        <li>Implemented cloud-native architecture for data pipeline development</li>
                        <li>Demonstrated expertise in cloud computing and distributed systems</li>
                        <li>Technologies: AWS, Python, Cloud Computing, Data Processing</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">💼 PROFESSIONAL EXPERIENCE</h3>

                <div class="experience-item">
                    <div class="item-header">
                        <div class="item-title">Software Developer & Technical Instructor</div>
                        <div class="item-date">Sep 2023 - Present</div>
                    </div>
                    <div class="item-subtitle">Central Johannesburg TVET College</div>
                    <ul class="item-bullets">
                        <li>Developed and delivered technical curriculum for software development and financial technology systems</li>
                        <li>Created educational software solutions and training materials for digital literacy programs</li>
                        <li>Mentored students in programming concepts, problem-solving methodologies, and technical skills</li>
                        <li>Implemented technology-enhanced learning solutions to improve educational outcomes</li>
                        <li>Collaborated with academic teams to integrate modern software development practices into curriculum</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="item-header">
                        <div class="item-title">Data Analyst & Systems Specialist</div>
                        <div class="item-date">2020 (3-6 Months)</div>
                    </div>
                    <div class="item-subtitle">Believers Care Society Academy</div>
                    <ul class="item-bullets">
                        <li>Designed and implemented automated data processing systems to handle large datasets</li>
                        <li>Developed data validation algorithms to ensure accuracy and integrity</li>
                        <li>Created reporting dashboards and analytics tools for organizational decision-making</li>
                        <li>Optimized database structures and queries for improved performance</li>
                        <li>Collaborated with cross-functional teams to streamline data workflows</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="item-header">
                        <div class="item-title">Technical Support Specialist</div>
                        <div class="item-date">2019 (0-3 Months)</div>
                    </div>
                    <div class="item-subtitle">Book Dash</div>
                    <ul class="item-bullets">
                        <li>Provided comprehensive technical support for digital events and technology infrastructure</li>
                        <li>Implemented automated setup procedures and troubleshooting protocols</li>
                        <li>Developed technical documentation and user guides for event technology systems</li>
                        <li>Collaborated with development teams to optimize event technology solutions</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">EDUCATION</h3>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Software Engineering</div>
                        <div class="item-date">2023 - Present</div>
                    </div>
                    <div class="item-subtitle">WeThinkCode_</div>
                    <div class="item-description">Intensive software engineering program focusing on practical coding skills, problem-solving, and industry-relevant technologies.</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Information Technology and Computer Science (NQF Level 4)</div>
                        <div class="item-date">Expected 2025</div>
                    </div>
                    <div class="item-subtitle">Central Johannesburg TVET College</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Office Administration (NQF Level 3)</div>
                        <div class="item-date">2020</div>
                    </div>
                    <div class="item-subtitle">Believers Care Society Academy</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Grade 11</div>
                        <div class="item-date">2017</div>
                    </div>
                    <div class="item-subtitle">City Rand College</div>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">🎓 PROFESSIONAL CERTIFICATIONS</h3>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">SAP Tech Consultant Certification</div>
                        <div class="item-date">2024</div>
                    </div>
                    <div class="item-subtitle">SAP</div>
                    <div class="item-description">Professional certification in SAP enterprise solutions, demonstrating expertise in business process optimization and enterprise software implementation.</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Java Programming Certification</div>
                        <div class="item-date">2024</div>
                    </div>
                    <div class="item-subtitle">Oracle</div>
                    <div class="item-description">Professional-level Java programming skills and enterprise development practices.</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">C# Programming Certification</div>
                        <div class="item-date">2024</div>
                    </div>
                    <div class="item-subtitle">Microsoft</div>
                    <div class="item-description">Advanced C# programming and .NET framework development capabilities.</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Machine Learning Specialization</div>
                        <div class="item-date">2023</div>
                    </div>
                    <div class="item-subtitle">Coursera - Stanford University</div>
                    <div class="item-description">Comprehensive understanding of machine learning algorithms and their applications.</div>
                </div>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">Python Programming Certification</div>
                        <div class="item-date">2023</div>
                    </div>
                    <div class="item-subtitle">Python Institute</div>
                    <div class="item-description">Advanced Python programming including OOP, data structures, and algorithms.</div>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">📚 ADDITIONAL COURSES</h3>

                <div class="education-item">
                    <div class="item-header">
                        <div class="item-title">ICT Courses</div>
                        <div class="item-date">Aug 2023</div>
                    </div>
                    <ul class="item-bullets">
                        <li>Help Desk Assistant</li>
                        <li>Data Capturer</li>
                        <li>Beginner Coder</li>
                        <li>Call Centre Operator</li>
                        <li>Computer Basics</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">💪 CORE COMPETENCIES & INTERESTS</h3>

                <div class="skills-container">
                    <span class="skill">Competitive Programming</span>
                    <span class="skill">Algorithm Design</span>
                    <span class="skill">System Architecture</span>
                    <span class="skill">Problem Solving</span>
                    <span class="skill">Code Optimization</span>
                    <span class="skill">Agile Development</span>
                    <span class="skill">Team Leadership</span>
                    <span class="skill">Technical Mentoring</span>
                    <span class="skill">AI Research</span>
                    <span class="skill">Open Source Contribution</span>
                    <span class="skill">Cloud Architecture</span>
                    <span class="skill">Continuous Learning</span>
                    <span class="skill">Innovation</span>
                    <span class="skill">Hackathon Participation</span>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">LANGUAGES</h3>

                <div class="skills-container">
                    <span class="skill">English (Fluent)</span>
                    <span class="skill">Swazi (Native)</span>
                    <span class="skill">Zulu (Native)</span>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">REFERENCES</h3>

                <div class="references">
                    <div class="reference">
                        <div class="reference-name">Mr. Alex Iheme</div>
                        <div class="reference-title">Employer</div>
                        <div class="reference-contact">📱 073 538 2855</div>
                    </div>

                    <div class="reference">
                        <div class="reference-name">Ms. Julia Norr</div>
                        <div class="reference-title">Employer</div>
                        <div class="reference-contact">📱 074 464 5588</div>
                    </div>

                    <div class="reference">
                        <div class="reference-name">Ms. Massana Mabitsela</div>
                        <div class="reference-title">Teacher / Educator</div>
                        <div class="reference-contact">📱 073 132 8146</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="print-button" onclick="window.print()">Print Resume</button>

    <script>
        // Add event listener for print button
        document.addEventListener('DOMContentLoaded', function() {
            const printButton = document.querySelector('.print-button');
            printButton.addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html>
