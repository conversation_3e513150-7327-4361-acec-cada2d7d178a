{"name": "my-portfolio", "version": "1.0.0", "description": "Welcome to  my portfolio !", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "generate-pdf": "node scripts/generate-pdf.js", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "repository": {"type": "git", "url": "git+https://github.com/BhekumusaEric/My-Portfolio.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/BhekumusaEric/My-Portfolio/issues"}, "homepage": "https://bhekumusaeric.github.io/My-Portfolio", "dependencies": {"framer-motion": "^4.1.17", "react": "^16.14.0", "react-dom": "^16.14.0", "react-router-dom": "^5.3.0", "react-scripts": "4.0.3", "styled-components": "^5.3.3", "three": "^0.135.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.27.1", "gh-pages": "^6.3.0"}}