@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Fira+Code:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #0969DA;
  --secondary-color: #1F222E;
  --accent-color: #F8D866;
  --dark-color: #121212;
  --light-color: #f5f5f5;
}

html,
body {
  padding: 0;
  margin: 0;
  font-family: 'Inter', sans-serif;
  scroll-behavior: smooth;
  background-color: var(--dark-color);
  color: var(--light-color);
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

.gradient-text {
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.canvas-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
}

.section {
  min-height: 100vh;
  padding: 6rem 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media (max-width: 768px) {
  .section {
    padding: 4rem 1rem;
  }
}
